<!DOCTYPE html>
<html lang="en">
    <head>
        <!-- 基础HTML5文档设置 -->
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <!-- 移动端视口设置：禁用缩放，适配全屏显示 -->
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <!-- 页面标题：FXAA后处理抗锯齿示例 -->
        <title>OGL • Post FXAA (Fast Approximate Anti-Aliasing)</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <!-- 页面信息显示：FXAA快速近似抗锯齿技术演示 -->
        <div class="Info">Post FXAA (Fast Approximate Anti-Aliasing)</div>
        <script type="module">
            // 导入OGL库的核心模块
            // Post: 后处理管线类，用于实现屏幕空间效果
            // Vec2: 二维向量类，用于分辨率等参数
            // Box: 立方体几何体，用作测试对象
            // NormalProgram: 法线可视化着色器程序
            import { Renderer, Camera, Mesh, Vec2, Post, Box, NormalProgram } from '../src/index.js';

            // FXAA后处理片段着色器
            // FXAA (Fast Approximate Anti-Aliasing) 是一种高效的屏幕空间抗锯齿技术
            const fragment = /* glsl */ `
                precision highp float;

                // 前一个渲染通道的输出纹理，默认名称为'tMap'
                // 可以通过添加通道时的'textureUniform'属性来更改此名称
                uniform sampler2D tMap;

                // 屏幕分辨率，用于计算像素偏移
                uniform vec2 uResolution;

                // 从顶点着色器传入的UV坐标
                varying vec2 vUv;

                // FXAA核心算法函数
                // 参数：tex=输入纹理, uv=当前像素UV坐标, resolution=屏幕分辨率
                vec4 fxaa(sampler2D tex, vec2 uv, vec2 resolution) {
                    // 计算单个像素在UV空间中的大小
                    vec2 pixel = vec2(1) / resolution;

                    // RGB到亮度的转换权重（基于人眼对不同颜色的敏感度）
                    // 绿色权重最高(0.587)，红色次之(0.299)，蓝色最低(0.114)
                    vec3 l = vec3(0.299, 0.587, 0.114);

                    // 采样当前像素周围的4个对角邻居像素，并计算其亮度
                    float lNW = dot(texture2D(tex, uv + vec2(-1, -1) * pixel).rgb, l); // 左上
                    float lNE = dot(texture2D(tex, uv + vec2( 1, -1) * pixel).rgb, l); // 右上
                    float lSW = dot(texture2D(tex, uv + vec2(-1,  1) * pixel).rgb, l); // 左下
                    float lSE = dot(texture2D(tex, uv + vec2( 1,  1) * pixel).rgb, l); // 右下
                    float lM  = dot(texture2D(tex, uv).rgb, l);                        // 中心像素

                    // 计算局部亮度的最小值和最大值，用于边缘检测
                    float lMin = min(lM, min(min(lNW, lNE), min(lSW, lSE)));
                    float lMax = max(lM, max(max(lNW, lNE), max(lSW, lSE)));

                    // 计算边缘方向向量
                    // 通过比较对角线上像素的亮度差异来确定边缘的主要方向
                    vec2 dir = vec2(
                        -((lNW + lNE) - (lSW + lSE)), // 水平方向的亮度梯度
                        ((lNW + lSW) - (lNE + lSE))   // 垂直方向的亮度梯度
                    );

                    // 计算方向缩减因子，防止在低对比度区域过度处理
                    // 0.03125 = 1/32, 0.0078125 = 1/128
                    float dirReduce = max((lNW + lNE + lSW + lSE) * 0.03125, 0.0078125);

                    // 计算方向向量的倒数最小值，用于归一化
                    float rcpDirMin = 1.0 / (min(abs(dir.x), abs(dir.y)) + dirReduce);

                    // 限制方向向量的长度，并转换为像素空间
                    // 限制在[-8, 8]像素范围内，防止过度模糊
                    dir = min(vec2(8, 8), max(vec2(-8, -8), dir * rcpDirMin)) * pixel;

                    // 第一次采样：沿边缘方向进行较窄的采样
                    // 在边缘方向上采样两个点，距离为1/3和2/3，然后取平均
                    vec3 rgbA = 0.5 * (
                        texture2D(tex, uv + dir * (1.0 / 3.0 - 0.5)).rgb +
                        texture2D(tex, uv + dir * (2.0 / 3.0 - 0.5)).rgb);

                    // 第二次采样：进行更宽的采样范围
                    // 结合第一次采样结果(50%)和更远距离的采样点(25%+25%)
                    vec3 rgbB = rgbA * 0.5 + 0.25 * (
                        texture2D(tex, uv + dir * -0.5).rgb +
                        texture2D(tex, uv + dir * 0.5).rgb);

                    // 计算第二次采样结果的亮度
                    float lB = dot(rgbB, l);

                    // 根据亮度范围决定使用哪个采样结果
                    // 如果rgbB的亮度在[lMin, lMax]范围内，使用rgbB（更宽的采样）
                    // 否则使用rgbA（更窄的采样），避免过度模糊
                    return mix(
                        vec4(rgbB, 1),  // 更宽采样的结果
                        vec4(rgbA, 1),  // 更窄采样的结果
                        max(sign(lB - lMin), 0.0) * max(sign(lB - lMax), 0.0)
                    );
                }

                // 主函数：实现分屏对比显示
                void main() {
                    // 获取原始像素颜色（未经抗锯齿处理）
                    vec4 raw = texture2D(tMap, vUv);

                    // 应用FXAA抗锯齿算法
                    vec4 aa = fxaa(tMap, vUv, uResolution);

                    // 分屏显示：左半屏显示原始图像，右半屏显示FXAA处理后的图像
                    // step(0.5, vUv.x) 在x > 0.5时返回1.0，否则返回0.0
                    gl_FragColor = mix(raw, aa, step(0.5, vUv.x));

                    // 将左半屏稍微变暗，以便更清楚地区分两侧
                    // step(vUv.x, 0.5) 在x <= 0.5时返回1.0，否则返回0.0
                    gl_FragColor -= step(vUv.x, 0.5) * 0.1;
                }
            `;

            {
                // 1. 初始化渲染器
                // dpr: 1 设置设备像素比为1，避免高DPI设备上的性能问题
                const renderer = new Renderer({ dpr: 1 });
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);

                // 设置白色背景，便于观察抗锯齿效果
                gl.clearColor(1, 1, 1, 1);

                // 2. 创建相机
                const camera = new Camera(gl, { fov: 35 });
                camera.position.set(0, 1, 5); // 相机位置：稍微向上和向后
                camera.lookAt([0, 0, 0]); // 看向原点

                // 3. 创建后处理管线
                // Post类会自动复制当前渲染器的参数（宽度、高度、DPI）
                const post = new Post(gl);

                // 4. 创建分辨率uniform变量，用于FXAA算法
                const resolution = { value: new Vec2() };

                // 5. 窗口大小调整处理函数
                function resize() {
                    // 更新渲染器尺寸
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    // 更新相机宽高比
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });

                    // 重要：调整后处理管线大小，因为渲染目标需要重新创建
                    post.resize();
                    // 更新分辨率uniform，FXAA算法需要准确的像素大小信息
                    resolution.value.set(gl.canvas.width, gl.canvas.height);
                }
                window.addEventListener('resize', resize, false);
                resize(); // 初始化时调用一次

                // 6. 创建测试场景
                const geometry = new Box(gl); // 立方体几何体，用于展示抗锯齿效果
                // 使用法线程序：将法线向量可视化为颜色，产生彩色渐变效果
                // 这种渐变在边缘处容易产生锯齿，非常适合测试抗锯齿算法
                const mesh = new Mesh(gl, { geometry, program: new NormalProgram(gl) });

                // 7. 添加FXAA后处理通道
                // 像创建Program一样添加通道，可以使用'enabled'属性来切换通道
                const pass = post.addPass({
                    // 如果不传入，通道将使用类内部的默认顶点/片段着色器
                    fragment, // 使用我们定义的FXAA片段着色器
                    uniforms: {
                        uResolution: resolution, // 传入屏幕分辨率
                    },
                });

                // 8. 启动渲染循环
                requestAnimationFrame(update);
                function update() {
                    requestAnimationFrame(update);

                    // 旋转立方体以展示动态的抗锯齿效果
                    mesh.rotation.y -= 0.005; // 绕Y轴旋转
                    mesh.rotation.x -= 0.01; // 绕X轴旋转（稍快一些）

                    // 使用后处理渲染替代常规渲染
                    // post.render会先渲染场景到纹理，然后应用FXAA通道
                    post.render({ scene: mesh, camera });
                }
            }
        </script>
    </body>
</html>
