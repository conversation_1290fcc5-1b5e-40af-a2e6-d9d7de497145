# 透明度排序与混合

## 📋 概述

透明度渲染是3D图形学中的经典难题之一。本文档详细解释WebGL中透明对象的深度排序机制，以及为什么必须采用特定的渲染顺序。

## 🔍 核心问题：为什么需要排序？

### 📐 混合公式的非交换性

WebGL透明度混合使用以下公式：
```glsl
最终颜色 = 源颜色 × 源Alpha + 目标颜色 × (1 - 源Alpha)
```

**关键问题**：这个公式**不满足交换律**！
- `A混合B ≠ B混合A`
- 渲染顺序直接影响最终视觉效果

### 🎨 错误排序的视觉问题

假设场景中有两个透明叶子：
- **红色叶子**（距离相机5单位，Alpha=0.5）
- **蓝色叶子**（距离相机3单位，Alpha=0.5）

#### ❌ 错误顺序（从前往后）：
```
步骤1: 渲染蓝色叶子 → 颜色缓冲区 = 纯蓝色
步骤2: 渲染红色叶子 → 最终颜色 = 红色×0.5 + 蓝色×0.5 = 错误的紫色
```

#### ✅ 正确顺序（从后往前）：
```
步骤1: 渲染红色叶子 → 颜色缓冲区 = 纯红色
步骤2: 渲染蓝色叶子 → 最终颜色 = 蓝色×0.5 + 红色×0.5 = 正确的紫色
```

## 🔧 WebGL渲染管线中的处理

### 📊 对象分类与排序策略

```javascript
// OGL框架内部排序逻辑（简化版）
function sortObjects(objects, camera) {
    const opaque = [];      // 不透明对象
    const transparent = []; // 透明对象
    
    // 1. 分类对象
    objects.forEach(obj => {
        if (obj.program.transparent) {
            transparent.push(obj);
        } else {
            opaque.push(obj);
        }
    });
    
    // 2. 计算距离并排序
    opaque.sort((a, b) => a.distanceToCamera - b.distanceToCamera);      // 从前往后
    transparent.sort((a, b) => b.distanceToCamera - a.distanceToCamera); // 从后往前
    
    // 3. 返回正确的渲染顺序
    return [...opaque, ...transparent];
}
```

### 🎯 渲染顺序的技术原理

#### **1. 不透明对象：从前往后渲染**
- **Early-Z优化**：GPU可以提前丢弃被遮挡的片段
- **性能提升**：减少不必要的片段着色器计算
- **深度缓冲区写入**：`gl.depthMask(true)`
- **深度测试**：`gl.enable(GL_DEPTH_TEST)`

#### **2. 透明对象：从后往前渲染**
- **正确混合**：确保视觉上正确的透明效果
- **深度只读**：`gl.depthMask(false)` - 不修改深度缓冲区
- **累积混合**：每个透明对象都与之前的结果混合
- **避免遮挡**：后面的透明对象仍能被看到

## 💡 深度缓冲区的作用差异

### 不透明对象的深度处理
```javascript
// 不透明对象渲染设置
gl.enable(GL_DEPTH_TEST);  // 启用深度测试
gl.depthMask(true);        // 允许写入深度缓冲区
gl.depthFunc(GL_LESS);     // 只渲染更近的片段
```

### 透明对象的深度处理
```javascript
// 透明对象渲染设置
gl.enable(GL_DEPTH_TEST);  // 启用深度测试（用于与不透明对象比较）
gl.depthMask(false);       // 禁止写入深度缓冲区
gl.enable(GL_BLEND);       // 启用混合
gl.blendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
```

## 🎨 实际示例：sort-transparency.html

### 关键代码片段
```javascript
// 材质设置
const program = new Program(gl, {
    vertex,
    fragment,
    uniforms: {
        tMap: { value: texture },
        uColor: { value: new Color('#ffc219') },
    },
    transparent: true, // ⭐ 标记为透明对象
    cullFace: false,   // ⭐ 双面渲染
});

// 片段着色器中的透明度处理
float alpha = texture2D(tMap, vUv).g;  // 使用绿色通道作为透明度
gl_FragColor.a = alpha;
if (alpha < 0.01) discard;             // 性能优化：丢弃几乎透明的片段
```

### 自动排序机制
```javascript
// OGL渲染器自动处理排序
renderer.render({ scene, camera });

// 内部流程：
// 1. 遍历场景中的所有对象
// 2. 计算每个对象到相机的距离
// 3. 按透明度属性分类
// 4. 分别排序：不透明（前→后），透明（后→前）
// 5. 按顺序渲染
```

## ⚠️ 常见问题与限制

### 1. 几何体内部面排序
- **问题**：单个几何体内部的三角形面仍可能有排序问题
- **解决方案**：需要额外的面级排序算法

### 2. 循环重叠问题
- **问题**：三个或更多透明对象相互重叠时无完美解决方案
- **示例**：A遮挡B，B遮挡C，C遮挡A
- **解决方案**：使用Order-Independent Transparency (OIT) 技术

### 3. 性能开销
- **问题**：每帧都需要重新计算距离和排序
- **优化**：使用空间分割、LOD等技术减少排序对象数量

## 🔬 高级技术

### Order-Independent Transparency (OIT)
- **A-Buffer**：存储所有片段信息，后处理排序
- **Weighted Blended OIT**：使用权重函数近似正确排序
- **Depth Peeling**：多次渲染，逐层剥离透明层

### 混合模式变体
```javascript
// 标准透明度混合
gl.blendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

// 加法混合（发光效果）
gl.blendFunc(GL_SRC_ALPHA, GL_ONE);

// 乘法混合（阴影效果）
gl.blendFunc(GL_DST_COLOR, GL_ZERO);
```

## 📚 相关文件

- **示例文件**：`examples/sort-transparency.html`
- **核心实现**：`src/core/Renderer.js`
- **相关概念**：深度测试、混合模式、渲染管线

## 🎯 学习要点

1. **理解混合公式的非交换性**
2. **掌握不同对象类型的排序策略**
3. **了解深度缓冲区在透明度渲染中的作用**
4. **认识透明度渲染的性能影响**
5. **探索高级透明度技术的应用场景**

透明度排序是现代3D图形引擎必须解决的核心问题，正确理解其原理对于创建高质量的3D应用至关重要。
