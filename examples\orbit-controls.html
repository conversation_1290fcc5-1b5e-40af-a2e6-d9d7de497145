<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <!-- 设置移动端视口，禁用用户缩放以确保轨道控制器的交互体验 -->
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Orbit Controls</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <!-- 信息面板：展示轨道控制器示例和缩放模式选择器 -->
        <div class="Info">
            Orbit Controls. Model by Google Poly.
            <!-- 缩放模式选择器：提供两种不同的缩放实现方式 -->
            <div id="dropdown">
                <label for="zoom-style">Zoom Style:</label>
                <select name="zoom-style" id="zoom-style">
                    <!-- Dolly模式：通过移动相机位置实现缩放（物理上的前进后退） -->
                    <option value="dolly">Dolly</option>
                    <!-- FOV模式：通过改变相机视野角度实现缩放（透视变化） -->
                    <option value="fov">FOV</option>
                </select>
            </div>
        </div>

        <script type="module">
            // 导入OGL框架的核心组件
            // Orbit: 轨道控制器，实现围绕目标点的相机控制（旋转、缩放、平移）
            import { Renderer, Camera, Transform, Texture, Program, Geometry, Mesh, Vec3, Orbit } from '../src/index.js';

            // 顶点着色器：处理3D模型的顶点变换和法线计算
            const vertex = /* glsl */ `
                // 输入属性：从几何体获取的顶点数据
                attribute vec2 uv;       // 纹理坐标，用于纹理映射
                attribute vec3 position; // 顶点位置（模型空间）
                attribute vec3 normal;   // 顶点法线向量（模型空间）

                // 变换矩阵：用于坐标空间转换
                uniform mat4 modelViewMatrix;  // 模型-视图矩阵（模型空间 → 视图空间）
                uniform mat4 projectionMatrix; // 投影矩阵（视图空间 → 裁剪空间）
                uniform mat3 normalMatrix;     // 法线矩阵（用于变换法线向量）

                // 输出变量：传递给片段着色器的插值数据
                varying vec2 vUv;     // 插值后的纹理坐标
                varying vec3 vNormal; // 插值后的世界空间法线

                void main() {
                    // 直接传递纹理坐标（无需变换）
                    vUv = uv;

                    // 将法线从模型空间变换到世界空间并归一化
                    // normalMatrix确保法线在非均匀缩放下保持正确
                    vNormal = normalize(normalMatrix * normal);

                    // 顶点位置变换：模型空间 → 视图空间 → 裁剪空间
                    // 这是标准的MVP（Model-View-Projection）变换流程
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // 片段着色器：计算每个像素的最终颜色
            const fragment = /* glsl */ `
                precision highp float; // 使用高精度浮点数确保渲染质量

                // 纹理采样器：用于获取模型表面的颜色信息
                uniform sampler2D tMap;

                // 从顶点着色器接收的插值数据
                varying vec2 vUv;     // 当前像素的纹理坐标
                varying vec3 vNormal; // 当前像素的世界空间法线

                void main() {
                    // 从纹理中采样获取基础颜色（漫反射颜色）
                    vec3 tex = texture2D(tMap, vUv).rgb;

                    // 重新归一化法线向量（插值可能导致长度变化）
                    vec3 normal = normalize(vNormal);

                    // 定义光源方向（世界空间中的方向光）
                    // 光源位置在右上前方，创造自然的照明效果
                    vec3 light = normalize(vec3(0.5, 1.0, -0.3));

                    // 计算兰伯特光照：dot(N, L) 表示法线与光线方向的夹角余弦值
                    // 乘以0.15控制光照强度，避免过度明亮
                    float shading = dot(normal, light) * 0.15;

                    // 最终颜色 = 基础纹理颜色 + 光照贡献
                    // 这是简化的光照模型，只考虑漫反射
                    gl_FragColor.rgb = tex + shading;
                    gl_FragColor.a = 1.0; // 完全不透明
                }
            `;

            {
                // === 渲染器初始化 ===
                // 创建WebGL渲染器，dpr: 2 支持高分辨率显示（如Retina屏幕）
                const renderer = new Renderer({ dpr: 2 });
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);

                // 设置清除颜色为白色，创建干净的背景
                gl.clearColor(1, 1, 1, 1);

                // === 相机设置 ===
                // 创建透视相机，45度视野角提供自然的视觉效果
                const camera = new Camera(gl, { fov: 45 });

                // 设置相机初始位置：左后上方，形成良好的观察角度
                // 这个位置让用户能清楚看到3D模型的立体结构
                camera.position.set(-2, 1, 2);

                // === 轨道控制器设置 ===
                // 创建轨道控制器，实现围绕目标点的相机交互控制
                // 支持鼠标/触摸操作：拖拽旋转、滚轮缩放、右键平移
                const controls = new Orbit(camera, {
                    // 设置轨道中心点，相机将围绕此点旋转
                    // y=0.7 让旋转中心稍微偏上，更适合观察模型
                    target: new Vec3(0, 0.7, 0),
                });

                // === 缩放模式切换 ===
                // 监听下拉菜单变化，动态切换缩放实现方式
                document.querySelector('#dropdown').addEventListener('change', ({ target: { value } }) => {
                    // dolly: 物理移动相机位置（真实的前进后退）
                    // fov: 改变视野角度（类似变焦镜头效果）
                    controls.zoomStyle = value;
                });

                // === 窗口大小调整处理 ===
                function resize() {
                    // 更新渲染器尺寸以匹配窗口大小
                    renderer.setSize(window.innerWidth, window.innerHeight);

                    // 重新计算相机的宽高比，保持正确的透视投影
                    // 避免图像在窗口尺寸变化时出现拉伸变形
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize(); // 立即执行一次以设置初始尺寸

                // === 场景根节点 ===
                // Transform作为场景图的根节点，管理所有3D对象的层次结构
                const scene = new Transform();

                // === 纹理加载 ===
                // 创建纹理对象用于模型表面贴图
                const texture = new Texture(gl);
                const img = new Image();

                // 异步加载图片，完成后将其设置为纹理的图像数据
                // 这种方式避免阻塞主线程，提供更好的用户体验
                img.onload = () => (texture.image = img);
                img.src = 'assets/macaw.jpg'; // 金刚鹦鹉纹理图片

                // === 着色器程序创建 ===
                // 将顶点着色器和片段着色器组合成完整的渲染程序
                const program = new Program(gl, {
                    vertex, // 顶点着色器代码
                    fragment, // 片段着色器代码
                    uniforms: {
                        // 将纹理绑定到着色器的tMap采样器
                        tMap: { value: texture },
                    },
                    // 禁用背面剔除，确保模型内部也能被看到
                    // 这对于某些复杂模型或透明效果很重要
                    cullFace: false,
                });

                // === 3D模型加载 ===
                let mesh; // 存储加载完成的网格对象
                loadModel(); // 开始异步加载模型

                async function loadModel() {
                    // 从JSON文件异步加载模型数据
                    // 这种格式包含了顶点位置、纹理坐标和法线信息
                    const data = await (await fetch(`assets/macaw.json`)).json();

                    // 创建几何体对象，包含模型的所有顶点数据
                    const geometry = new Geometry(gl, {
                        // 顶点位置数据：每个顶点3个分量(x, y, z)
                        position: { size: 3, data: new Float32Array(data.position) },
                        // 纹理坐标数据：每个顶点2个分量(u, v)
                        uv: { size: 2, data: new Float32Array(data.uv) },
                        // 法线向量数据：每个顶点3个分量(nx, ny, nz)
                        normal: { size: 3, data: new Float32Array(data.normal) },
                    });

                    // 创建网格对象，结合几何体和着色器程序
                    mesh = new Mesh(gl, { geometry, program });

                    // 将网格添加到场景中，建立父子关系
                    mesh.setParent(scene);
                }

                // === 渲染循环启动 ===
                requestAnimationFrame(update);

                function update(t) {
                    // 递归调用，创建持续的渲染循环
                    // 使用requestAnimationFrame确保与显示器刷新率同步
                    requestAnimationFrame(update);

                    // === 轨道控制器更新 ===
                    // 每帧都必须更新控制器以处理用户输入
                    // 包括鼠标/触摸事件的处理和相机矩阵的计算
                    controls.update();

                    // === 场景渲染 ===
                    // 使用当前的场景和相机状态进行渲染
                    // 轨道控制器的变化会自动反映在相机矩阵中
                    renderer.render({ scene, camera });
                }
            }
        </script>
    </body>
</html>
