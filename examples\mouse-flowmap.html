<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <!-- 禁用用户缩放，确保鼠标坐标计算的准确性 -->
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Mouse Flowmap</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <!--
        鼠标流场映射示例：
        演示如何使用鼠标交互创建动态流场效果，模拟液体表面的扰动
        流场映射是一种将速度信息编码到纹理中的技术，常用于：
        - 水面波纹模拟
        - 烟雾和火焰效果
        - 布料变形
        - 粒子系统引导
        -->
        <div class="Info">
            Mouse Flowmap. Texture by
            <a href="https://www.deviantart.com/berserkitty/art/Seamless-Cartoon-styled-Water-Texture-743787929" target="_blank">BerserKitty</a>
        </div>
        <script type="module">
            // 导入OGL框架组件
            // Flowmap: 流场映射类，用于生成和管理速度场纹理
            // Triangle: 全屏三角形几何体，用于后处理效果
            import { Renderer, Program, Texture, Mesh, Vec2, Flowmap, Triangle } from '../src/index.js';

            // 顶点着色器：全屏渲染的简单通道着色器
            const vertex = /* glsl */ `
                // 输入属性：2D几何体数据
                attribute vec2 uv;       // 纹理坐标 [0,1]
                attribute vec2 position; // 顶点位置 [-1,1] (NDC空间)

                // 输出到片段着色器
                varying vec2 vUv; // 传递纹理坐标

                void main() {
                    // 直接传递纹理坐标，无需变换
                    vUv = uv;

                    // 直接使用NDC坐标，无需MVP变换
                    // 全屏三角形覆盖整个屏幕空间
                    gl_Position = vec4(position, 0, 1);
                }
            `;

            // 片段着色器：实现流场驱动的纹理扭曲效果
            const fragment = /* glsl */ `
                precision highp float;

                // 纹理输入
                uniform sampler2D tWater; // 水面纹理（被扭曲的目标纹理）
                uniform sampler2D tFlow;  // 流场纹理（速度场信息）
                uniform float uTime;      // 时间uniform，用于动画效果

                // 从顶点着色器接收的纹理坐标
                varying vec2 vUv;

                void main() {
                    // === 流场数据解码 ===
                    // 从流场纹理中读取速度信息
                    // R通道：X方向速度分量
                    // G通道：Y方向速度分量
                    // B通道：速度矢量的长度（magnitude）
                    vec3 flow = texture2D(tFlow, vUv).rgb;

                    // === 纹理坐标扭曲 ===
                    // 使用片段坐标而非UV坐标，创建更自然的扭曲效果
                    vec2 uv = gl_FragCoord.xy / 600.0; // 归一化片段坐标

                    // 应用流场扭曲：将速度矢量添加到纹理坐标
                    // 0.05是扭曲强度系数，控制变形程度
                    uv += flow.xy * 0.05;

                    // 使用扭曲后的坐标采样水面纹理
                    vec3 tex = texture2D(tWater, uv).rgb;

                    // === 可视化模式切换 ===
                    // 在扭曲纹理和流场可视化之间振荡切换
                    // sin(uTime)产生[-1,1]的振荡
                    // smoothstep(-0.3, 0.7, ...)创建平滑的过渡
                    // flow * 0.5 + 0.5 将[-1,1]的流场值映射到[0,1]颜色范围
                    tex = mix(tex, flow * 0.5 + 0.5, smoothstep(-0.3, 0.7, sin(uTime)));

                    // 输出最终颜色
                    gl_FragColor.rgb = tex;
                    gl_FragColor.a = 1.0;
                }
            `;

            {
                // === 渲染器初始化 ===
                const renderer = new Renderer({ dpr: 2 });
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);

                // === 流场控制变量 ===
                let aspect = 1; // 屏幕宽高比，用于流场计算
                const mouse = new Vec2(-1); // 鼠标位置，-1表示未激活状态
                const velocity = new Vec2(); // 鼠标速度矢量

                // === 窗口大小调整处理 ===
                function resize() {
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    // 更新宽高比，确保流场在不同屏幕尺寸下正确显示
                    aspect = window.innerWidth / window.innerHeight;
                }
                window.addEventListener('resize', resize, false);
                resize();

                // === 流场映射系统 ===
                // Flowmap类管理双缓冲渲染目标，生成持续的速度场
                const flowmap = new Flowmap(gl);

                // === 全屏几何体 ===
                // 使用三角形而非四边形的优势：
                // 1. 减少顶点数量（3 vs 4）
                // 2. 避免对角线分割问题
                // 3. 确保完全覆盖屏幕空间
                // position范围：[-1,1] (NDC坐标)
                // uv范围：[0,1] (纹理坐标)
                const geometry = new Triangle(gl);

                // === 水面纹理设置 ===
                const texture = new Texture(gl, {
                    wrapS: gl.REPEAT, // 水平方向重复，创建无缝平铺
                    wrapT: gl.REPEAT, // 垂直方向重复，创建无缝平铺
                });
                const img = new Image();
                img.onload = () => (texture.image = img);
                img.src = 'assets/water.jpg'; // 加载水面纹理图片

                // === 着色器程序配置 ===
                const program = new Program(gl, {
                    vertex,
                    fragment,
                    uniforms: {
                        uTime: { value: 0 }, // 时间uniform，驱动动画效果
                        tWater: { value: texture }, // 水面纹理uniform

                        // === 流场纹理uniform ===
                        // 注意：这里直接使用flowmap.uniform而不是{value: ...}格式
                        // 原因：Flowmap类内部使用双缓冲技术，在两个渲染目标间切换
                        // 每次渲染后会自动更新uniform的value属性指向当前活跃的纹理
                        // 这种设计避免了手动管理纹理切换的复杂性
                        tFlow: flowmap.uniform,
                    },
                });

                // === 渲染网格 ===
                const mesh = new Mesh(gl, { geometry, program });

                // === 输入事件处理系统 ===

                // 检测设备是否支持触摸，选择合适的事件类型
                const isTouchCapable = 'ontouchstart' in window;
                if (isTouchCapable) {
                    // 移动设备：监听触摸事件
                    window.addEventListener('touchstart', updateMouse, false);
                    window.addEventListener('touchmove', updateMouse, false);
                } else {
                    // 桌面设备：监听鼠标事件
                    window.addEventListener('mousemove', updateMouse, false);
                }

                // === 速度计算变量 ===
                let lastTime; // 上一帧的时间戳
                const lastMouse = new Vec2(); // 上一帧的鼠标位置

                /**
                 * 鼠标/触摸事件处理函数
                 *
                 * 功能：
                 * 1. 统一处理鼠标和触摸事件
                 * 2. 计算归一化的位置坐标
                 * 3. 基于时间和位移计算速度矢量
                 * 4. 处理边界情况和性能优化
                 */
                function updateMouse(e) {
                    // === 事件坐标标准化 ===
                    // 处理触摸事件的坐标提取
                    if (e.changedTouches && e.changedTouches.length) {
                        e.x = e.changedTouches[0].pageX;
                        e.y = e.changedTouches[0].pageY;
                    }
                    // 处理鼠标事件的坐标提取
                    if (e.x === undefined) {
                        e.x = e.pageX;
                        e.y = e.pageY;
                    }

                    // === 坐标归一化 ===
                    // 将屏幕像素坐标转换为[0,1]范围，Y轴翻转以匹配纹理坐标系
                    // 纹理坐标系：原点在左下角，Y轴向上
                    // 屏幕坐标系：原点在左上角，Y轴向下
                    mouse.set(
                        e.x / gl.renderer.width, // X: [0, width] → [0, 1]
                        1 - e.y / gl.renderer.height // Y: [0, height] → [1, 0] (翻转)
                    );

                    // === 速度计算 ===
                    if (!lastTime) {
                        // 首帧初始化：记录初始时间和位置
                        lastTime = performance.now();
                        lastMouse.set(e.x, e.y);
                        return; // 首帧无法计算速度，直接返回
                    }

                    // 计算位置增量（像素单位）
                    const deltaX = e.x - lastMouse.x;
                    const deltaY = e.y - lastMouse.y;

                    // 更新位置记录
                    lastMouse.set(e.x, e.y);

                    // 计算时间增量
                    let time = performance.now();

                    // 防止除零错误：确保最小时间间隔为14ms（约60FPS）
                    let delta = Math.max(14, time - lastTime);
                    lastTime = time;

                    // 计算速度：像素/毫秒
                    // 这个速度值将被流场系统用于生成扰动
                    velocity.x = deltaX / delta;
                    velocity.y = deltaY / delta;

                    // 设置更新标志，防止静止时保持旧的速度值
                    velocity.needsUpdate = true;
                }

                // === 主渲染循环 ===
                requestAnimationFrame(update);

                /**
                 * 渲染循环函数
                 *
                 * 核心流程：
                 * 1. 处理输入状态管理
                 * 2. 更新流场参数
                 * 3. 执行流场计算
                 * 4. 渲染最终效果
                 */
                function update(t) {
                    requestAnimationFrame(update);

                    // === 输入状态管理 ===
                    // 当鼠标停止移动时重置状态，避免残留的速度值
                    if (!velocity.needsUpdate) {
                        mouse.set(-1); // -1表示鼠标未激活状态
                        velocity.set(0); // 清零速度
                    }
                    velocity.needsUpdate = false; // 重置更新标志

                    // === 流场参数更新 ===
                    flowmap.aspect = aspect; // 设置屏幕宽高比
                    flowmap.mouse.copy(mouse); // 传递鼠标位置

                    // === 速度平滑处理 ===
                    // 使用线性插值(lerp)平滑速度变化，创建更自然的流场效果
                    // velocity.len() > 0: 有运动时使用0.5的插值系数（快速响应）
                    // velocity.len() = 0: 静止时使用0.1的插值系数（缓慢衰减）
                    // 这种设计让流场效果有惯性，更符合物理直觉
                    flowmap.velocity.lerp(velocity, velocity.len() ? 0.5 : 0.1);

                    // === 流场计算 ===
                    // 执行流场的双缓冲渲染，更新速度场纹理
                    flowmap.update();

                    // === 时间uniform更新 ===
                    // 将毫秒转换为秒，用于着色器中的时间动画
                    program.uniforms.uTime.value = t * 0.001;

                    // === 最终渲染 ===
                    // 渲染应用了流场扭曲效果的水面纹理
                    renderer.render({ scene: mesh });
                }
            }
        </script>
    </body>
</html>
