<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全屏三角形 vs 屏幕大小对比演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .demo-section {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        canvas {
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
            background: white;
            display: block;
            margin: 0 auto 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .info-card {
            background: rgba(255,255,255,0.15);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        
        .vertex-info {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        
        .vertex-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            border: 2px solid white;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
        }
        
        button:hover {
            transform: translateY(-2px);
        }
        
        .legend {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            background: rgba(255,255,255,0.1);
            padding: 8px 15px;
            border-radius: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔺 全屏三角形 vs 屏幕大小对比演示</h1>
        
        <div class="demo-section">
            <h2>📐 NDC坐标系统中的位置关系</h2>
            <canvas id="coordinateCanvas" width="800" height="600"></canvas>
            
            <div class="legend">
                <div class="legend-item">
                    <div class="vertex-color" style="background: #4CAF50;"></div>
                    <span>顶点1: (-1, -1) 屏幕左下角</span>
                </div>
                <div class="legend-item">
                    <div class="vertex-color" style="background: #F44336;"></div>
                    <span>顶点2: (3, -1) 超出屏幕右侧</span>
                </div>
                <div class="legend-item">
                    <div class="vertex-color" style="background: #2196F3;"></div>
                    <span>顶点3: (-1, 3) 超出屏幕上方</span>
                </div>
                <div class="legend-item">
                    <div class="vertex-color" style="background: rgba(33, 150, 243, 0.3); border: 2px solid #2196F3;"></div>
                    <span>屏幕可见区域</span>
                </div>
            </div>
            
            <div class="controls">
                <button onclick="toggleAnimation()">🎬 切换动画</button>
                <button onclick="toggleGrid()">📊 切换网格</button>
                <button onclick="toggleUV()">🎯 切换UV映射</button>
            </div>
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <h3>📏 尺寸对比</h3>
                <p><strong>屏幕尺寸：</strong> 2×2 单位</p>
                <p><strong>三角形尺寸：</strong> 4×4 单位</p>
                <p><strong>比例：</strong> 三角形是屏幕的 <span style="color: #4CAF50; font-weight: bold;">2倍</span> 大</p>
            </div>
            
            <div class="info-card">
                <h3>🎯 顶点坐标</h3>
                <div class="vertex-info">
                    <div class="vertex-color" style="background: #4CAF50;"></div>
                    <span>顶点1: (-1, -1) UV(0, 0)</span>
                </div>
                <div class="vertex-info">
                    <div class="vertex-color" style="background: #F44336;"></div>
                    <span>顶点2: (3, -1) UV(2, 0)</span>
                </div>
                <div class="vertex-info">
                    <div class="vertex-color" style="background: #2196F3;"></div>
                    <span>顶点3: (-1, 3) UV(0, 2)</span>
                </div>
            </div>
            
            <div class="info-card">
                <h3>💡 设计优势</h3>
                <p>✓ 只需3个顶点（vs 四边形6个）</p>
                <p>✓ 避免对角线撕裂问题</p>
                <p>✓ GPU光栅化更高效</p>
                <p>✓ 确保完全覆盖屏幕</p>
            </div>
            
            <div class="info-card">
                <h3>🔧 应用场景</h3>
                <p>• 全屏后处理效果</p>
                <p>• 屏幕空间渲染</p>
                <p>• 纹理合成和混合</p>
                <p>• 滤镜和特效应用</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('coordinateCanvas');
        const ctx = canvas.getContext('2d');
        
        // 动画控制变量
        let animationEnabled = false;
        let showGrid = true;
        let showUV = false;
        let animationFrame = 0;
        
        // 坐标转换函数：NDC坐标 -> Canvas坐标
        function ndcToCanvas(x, y) {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const scale = 80; // 缩放因子
            
            return {
                x: centerX + x * scale,
                y: centerY - y * scale // Y轴翻转
            };
        }
        
        // 绘制网格
        function drawGrid() {
            if (!showGrid) return;
            
            ctx.strokeStyle = '#E0E0E0';
            ctx.lineWidth = 1;
            ctx.setLineDash([5, 5]);
            
            // 绘制网格线
            for (let i = -4; i <= 4; i++) {
                const pos = ndcToCanvas(i, 0);
                ctx.beginPath();
                ctx.moveTo(pos.x, 50);
                ctx.lineTo(pos.x, canvas.height - 50);
                ctx.stroke();
                
                const posY = ndcToCanvas(0, i);
                ctx.beginPath();
                ctx.moveTo(50, posY.y);
                ctx.lineTo(canvas.width - 50, posY.y);
                ctx.stroke();
            }
            
            ctx.setLineDash([]);
        }
        
        // 绘制坐标轴
        function drawAxes() {
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            
            // X轴
            ctx.beginPath();
            ctx.moveTo(50, canvas.height / 2);
            ctx.lineTo(canvas.width - 50, canvas.height / 2);
            ctx.stroke();
            
            // Y轴
            ctx.beginPath();
            ctx.moveTo(canvas.width / 2, 50);
            ctx.lineTo(canvas.width / 2, canvas.height - 50);
            ctx.stroke();
            
            // 坐标标签
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            
            for (let i = -3; i <= 3; i++) {
                if (i === 0) continue;
                const pos = ndcToCanvas(i, 0);
                ctx.fillText(i.toString(), pos.x, canvas.height / 2 + 20);
                
                const posY = ndcToCanvas(0, i);
                ctx.fillText(i.toString(), canvas.width / 2 + 20, posY.y + 5);
            }
        }
        
        // 绘制屏幕区域
        function drawScreen() {
            const topLeft = ndcToCanvas(-1, 1);
            const bottomRight = ndcToCanvas(1, -1);
            
            // 屏幕背景
            ctx.fillStyle = 'rgba(33, 150, 243, 0.2)';
            ctx.fillRect(topLeft.x, topLeft.y, bottomRight.x - topLeft.x, bottomRight.y - topLeft.y);
            
            // 屏幕边框
            ctx.strokeStyle = '#2196F3';
            ctx.lineWidth = 3;
            ctx.strokeRect(topLeft.x, topLeft.y, bottomRight.x - topLeft.x, bottomRight.y - topLeft.y);
            
            // 屏幕标签
            ctx.fillStyle = '#2196F3';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('📺 屏幕区域 (-1,1) 到 (1,-1)', canvas.width / 2, topLeft.y - 10);
        }
        
        // 绘制三角形
        function drawTriangle() {
            const v1 = ndcToCanvas(-1, -1); // 绿色顶点
            const v2 = ndcToCanvas(3, -1);  // 红色顶点
            const v3 = ndcToCanvas(-1, 3);  // 蓝色顶点
            
            // 三角形填充
            ctx.fillStyle = 'rgba(255, 152, 0, 0.1)';
            ctx.beginPath();
            ctx.moveTo(v1.x, v1.y);
            ctx.lineTo(v2.x, v2.y);
            ctx.lineTo(v3.x, v3.y);
            ctx.closePath();
            ctx.fill();
            
            // 三角形边框
            ctx.strokeStyle = '#FF9800';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制顶点
            const vertices = [
                { pos: v1, color: '#4CAF50', label: '顶点1\n(-1, -1)', uv: 'UV(0,0)' },
                { pos: v2, color: '#F44336', label: '顶点2\n(3, -1)', uv: 'UV(2,0)' },
                { pos: v3, color: '#2196F3', label: '顶点3\n(-1, 3)', uv: 'UV(0,2)' }
            ];
            
            vertices.forEach((vertex, index) => {
                // 顶点圆圈
                ctx.fillStyle = vertex.color;
                ctx.beginPath();
                ctx.arc(vertex.pos.x, vertex.pos.y, 8, 0, 2 * Math.PI);
                ctx.fill();
                
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                // 顶点标签
                ctx.fillStyle = vertex.color;
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                
                const lines = vertex.label.split('\n');
                lines.forEach((line, lineIndex) => {
                    ctx.fillText(line, vertex.pos.x, vertex.pos.y - 25 + lineIndex * 15);
                });
                
                // UV坐标标签
                if (showUV) {
                    ctx.fillStyle = '#666';
                    ctx.font = '10px Arial';
                    ctx.fillText(vertex.uv, vertex.pos.x, vertex.pos.y + 25);
                }
            });
        }
        
        // 绘制动画效果
        function drawAnimation() {
            if (!animationEnabled) return;
            
            animationFrame += 0.02;
            
            // 绘制动态光晕效果
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            ctx.save();
            ctx.globalAlpha = 0.3 + 0.2 * Math.sin(animationFrame);
            
            const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, 200);
            gradient.addColorStop(0, 'rgba(255, 152, 0, 0.3)');
            gradient.addColorStop(1, 'rgba(255, 152, 0, 0)');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.restore();
        }
        
        // 主绘制函数
        function draw() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制各个元素
            drawGrid();
            drawAxes();
            drawScreen();
            drawTriangle();
            drawAnimation();
            
            // 继续动画
            if (animationEnabled) {
                requestAnimationFrame(draw);
            }
        }
        
        // 控制函数
        function toggleAnimation() {
            animationEnabled = !animationEnabled;
            if (animationEnabled) {
                draw();
            } else {
                draw(); // 重绘一次以清除动画效果
            }
        }
        
        function toggleGrid() {
            showGrid = !showGrid;
            draw();
        }
        
        function toggleUV() {
            showUV = !showUV;
            draw();
        }
        
        // 初始绘制
        draw();
    </script>
</body>
</html>
