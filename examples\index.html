<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL WebGL Library</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Side">
            <a class="Title" href="https://github.com/oframe/ogl" target="_blank">OGL</a>
            <div class="SubTitle">Examples</div>

            <div class="Section">Geometry</div>
            <a class="Example" href="triangle-screen-shader.html">Triangle Screen Shader</a>
            <a class="Example" href="draw-modes.html">Draw Modes</a>
            <a class="Example" href="indexed-vs-non-indexed.html">Indexed vs Non-Indexed</a>
            <a class="Example" href="load-json.html">Load JSON (Javascript Object Notation)</a>
            <a class="Example" href="wireframe.html">Wireframe</a>
            <a class="Example" href="wire-mesh.html">WireMesh (wireframe)</a>
            <a class="Example" href="base-primitives.html">Base Primitives - Plane, Cube, Sphere</a>
            <a class="Example" href="particles.html">Particles</a>
            <a class="Example" href="instancing.html">Instancing</a>
            <a class="Example" href="instancing-gpu-picking.html">Instancing - GPU Picking</a>
            <div class="Example">Particle Depth Sort</div>
            <div class="Example">LODs (Level Of Detail)</div>
            <a class="Example" href="polylines.html">Polylines</a>
            <a class="Example" href="curves.html">Curves</a>
            <a class="Example" href="paths.html">Paths</a>
            <a class="Example" href="tube.html">Tube</a>
            <a class="Example" href="torus.html">Torus</a>
            <div class="Example">Load OBJ (Object file format)</div>
            <a class="Example" href="load-gltf.html">Load GLTF (Graphics Language Transmission Format)</a>
            <a class="Example" href="gltf-ktx2-basis.html">GLTF KTX2 Basis Extension</a>
            <a class="Example" href="helpers.html">Helpers</a>

            <div class="Section">Scene</div>
            <a class="Example" href="scene-graph.html">Scene Graph hierarchy</a>
            <a class="Example" href="sort-transparency.html">Sort Transparency</a>
            <a class="Example" href="frustum-culling.html">Frustum culling</a>

            <div class="Section">Interaction</div>
            <a class="Example" href="orbit-controls.html">Orbit controls</a>
            <a class="Example" href="raycasting.html">Projection and Raycasting</a>
            <a class="Example" href="mouse-flowmap.html">Mouse Flowmap</a>

            <div class="Section">Shading</div>
            <a class="Example" href="fog.html">Fog</a>
            <a class="Example" href="textures.html">Textures</a>
            <a class="Example" href="anisotropic.html">Anisotropic</a>
            <a class="Example" href="skydome.html">Skydome</a>
            <a class="Example" href="cube-map.html">Cube Map</a>
            <a class="Example" href="normal-maps.html">Normal Maps</a>
            <a class="Example" href="flat-shading-matcap.html">Flat Shading Matcap</a>
            <a class="Example" href="wireframe-shader.html">Wireframe Shader</a>
            <div class="Example">SDF Alpha test/clip (Signed Distance Fields)</div>
            <a class="Example" href="msdf-text.html">MSDF Text Glyphs (Multichannel Signed Distance Fields)</a>
            <a class="Example" href="point-lighting.html">Point lighting with specular highlights</a>
            <a class="Example" href="pbr.html">PBR (Physically Based Rendering)</a>
            <a class="Example" href="compressed-textures.html">Compressed Textures</a>
            <a class="Example" href="fresnel.html">Simple Fresnel Shader</a>

            <div class="Section">Frame Buffer</div>
            <a class="Example" href="render-to-texture.html">Render to texture</a>
            <a class="Example" href="post-fxaa.html">Post FXAA (Fast Approximate Anti-Aliasing)</a>
            <a class="Example" href="post-bloom.html">Post Bloom Effect</a>
            <a class="Example" href="mrt.html">MRT (Multiple Render Targets)</a>
            <div class="Example">Reflections</div>
            <a class="Example" href="shadow-maps.html">Shadow maps</a>
            <div class="Example">Distortion (refraction)</div>
            <a class="Example" href="post-fluid-distortion.html">Post Fluid Distortion</a>
            <div class="Example">Effects - DOF (Depth Of Field) + light rays + tone mapping</div>
            <a class="Example" href="gpgpu-particles.html">GPGPU Particles (General-Purpose computing on Graphics Processing Units)</a>

            <div class="Section">Animation</div>
            <a class="Example" href="skinning.html">Skinning</a>
            <div class="Example">Blendshapes</div>
            <div class="Example">Load Hierarchy Animation</div>

            <div class="Section">Stencil</div>
            <div class="Example">Stencil Shadows and Mirror</div>

            <div class="Section">Performance</div>
            <a class="Example" href="high-mesh-count.html">High mesh count</a>
        </div>

        <iframe class="Iframe" allow="autoplay" src=""></iframe>

        <a class="SideIcon">&lt;</a>
        <a class="CodeIcon" href="" target="_blank">&lt;&#47;&gt;</a>

        <script>
            const sourceLink = document.querySelector('.CodeIcon');
            const toggleLink = document.querySelector('.SideIcon');
            const exampleLinks = document.querySelectorAll('a.Example');
            const iFrame = document.querySelector('.Iframe');
            const sourcePath = 'https://github.com/oframe/ogl/tree/master/examples/';

            toggleLink.addEventListener('click', (e) => {
                e.preventDefault();
                document.body.toggleAttribute('data-hideSidebar');
            });

            // Load query example in iFrame if linked
            let loadQuery = location.search.split('src=');
            if (loadQuery[1]) {
                iFrame.src = loadQuery[1];
                sourceLink.href = sourcePath + loadQuery[1];
                highlight(loadQuery[1]);
            } else {
                // choose random example to show if none linked
                let target = exampleLinks[Math.floor(Math.random() * exampleLinks.length)];
                updateExample({ target });
            }

            exampleLinks.forEach((link) => {
                // 检查是否是外部链接（GitHub等）
                if (link.href && (link.href.includes('github.com') || (link.href.includes('://') && !link.href.includes(location.host)))) {
                    // 为外部链接添加target="_blank"
                    link.target = '_blank';
                } else if (link.href) {
                    // 为本地示例链接添加事件监听器
                    link.addEventListener('click', updateExample, false);
                }
            });

            function updateExample(e) {
                // 防止默认链接行为
                if (e.preventDefault) {
                    e.preventDefault();
                }

                // 检查是否是外部链接，如果是则不处理
                if (!e.target.href || e.target.href.includes('github.com') || (e.target.href.includes('://') && !e.target.href.includes(location.host))) {
                    return;
                }

                let src = e.target.href.split('examples/')[1];

                // 如果无法解析出示例文件名，则不处理
                if (!src) {
                    return;
                }

                // Allow user to cmd/ctrl + click to open in new tab
                if (e.metaKey || e.ctrlKey) {
                    window.open(`${location.origin}${location.pathname}?src=${src}`, '_blank');
                    return;
                }

                iFrame.src = e.target.href;
                sourceLink.href = sourcePath + src;
                highlight(src);

                // Update search query
                history.pushState(null, null, `${location.origin}${location.pathname}?src=${src}`);
            }

            // Highlight and update others
            function highlight(src) {
                exampleLinks.forEach((link) => {
                    let linkSrc = link.href.split('examples/')[1];
                    if (src === linkSrc) link.classList.add('active');
                    else link.classList.remove('active');
                });
            }
        </script>
    </body>
</html>
