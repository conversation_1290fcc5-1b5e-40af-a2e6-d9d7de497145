<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • MRT (Multiple Render Targets)</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info" style="color: #fff">MRT (Multiple Render Targets)</div>
        <script type="module">
            // 导入 OGL 框架的核心模块
            // RenderTarget: 用于创建多渲染目标 (MRT)
            // Program: 着色器程序管理
            // Triangle: 全屏三角形几何体，用于后处理
            import { Renderer, Camera, Transform, Geometry, Texture, RenderTarget, Program, Mesh, Vec3, Orbit, Triangle } from '../src/index.js';

            // ========================================
            // WebGL 1.0 着色器 (使用 gl_FragData)
            // ========================================

            // WebGL 1.0 顶点着色器
            // 功能：处理实例化渲染的几何体变换和随机化
            const vertex100 = /* glsl */ `
                // 顶点属性
                attribute vec3 position;    // 顶点位置
                attribute vec3 normal;      // 顶点法线
                attribute vec2 uv;          // 纹理坐标
                attribute vec3 offset;      // 实例偏移量 (用于实例化渲染)
                attribute vec3 random;      // 随机值 (用于变化每个实例)

                // 变换矩阵
                uniform mat4 modelMatrix;       // 模型矩阵
                uniform mat4 modelViewMatrix;   // 模型视图矩阵
                uniform mat4 projectionMatrix;  // 投影矩阵
                uniform mat3 normalMatrix;      // 法线矩阵

                // 传递给片段着色器的变量
                varying vec2 vUv;       // 纹理坐标
                varying vec3 vNormal;   // 世界空间法线
                varying vec3 vMPos;     // 世界空间位置

                // 2D旋转函数：将向量v绕原点旋转角度a
                void rotate2d(inout vec2 v, float a){
                    mat2 m = mat2(cos(a), -sin(a), sin(a),  cos(a));
                    v = m * v;
                }

                void main() {
                    vec3 pos = position;

                    // 随机缩放：基础大小0.8，随机增加0-0.3
                    pos *= 0.8 + random.y * 0.3;

                    // XZ平面随机旋转：基于随机值创建不同朝向
                    rotate2d(pos.xz, random.x * 6.28 + 4.0 * (random.y - 0.5));

                    // ZY平面随机旋转：创建更复杂的随机姿态
                    rotate2d(pos.zy, random.z * 0.9 * sin(random.x + random.z * 3.14));

                    // 应用实例偏移：在场景中分布多个实例
                    pos += offset * vec3(2.5, 0.2, 2.5);

                    // 传递数据到片段着色器
                    vUv = uv;
                    vNormal = normalize(normalMatrix * normal);  // 变换法线到世界空间
                    vMPos = (modelMatrix * vec4(pos, 1.0)).xyz;  // 计算世界空间位置

                    // 计算最终的屏幕空间位置
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
                }
            `;

            // WebGL 1.0 片段着色器 - 多渲染目标 (MRT) 实现
            // 功能：同时输出颜色和位置数据到两个不同的渲染目标
            const fragment100 = /* glsl */ `
                // 启用多渲染目标扩展 (WebGL 1.0 必需)
                #extension GL_EXT_draw_buffers : require

                precision highp float;

                // 纹理采样器
                uniform sampler2D tMap;     // 物体的颜色纹理

                // 从顶点着色器接收的数据
                varying vec2 vUv;       // 纹理坐标
                varying vec3 vNormal;   // 世界空间法线 (本例中未使用)
                varying vec3 vMPos;     // 世界空间位置

                void main() {
                    // MRT 输出：同时写入两个渲染目标

                    // 渲染目标 0：颜色信息
                    // 从纹理采样获取物体的颜色
                    gl_FragData[0] = texture2D(tMap, vUv);

                    // 渲染目标 1：位置和深度信息
                    // RGB: 世界空间位置坐标
                    // A: 屏幕空间深度值 (用于后续的深度测试和效果)
                    gl_FragData[1] = vec4(vMPos, gl_FragCoord.z);
                }
            `;

            // ========================================
            // WebGL 2.0 着色器 (使用显式输出声明)
            // ========================================

            // WebGL 2.0 顶点着色器
            // 功能：与 WebGL 1.0 版本相同，但使用现代 GLSL 语法
            const vertex300 = /* glsl */ `#version 300 es
                // 输入属性 (WebGL 2.0 使用 'in' 关键字)
                in vec3 position;    // 顶点位置
                in vec3 normal;      // 顶点法线
                in vec2 uv;          // 纹理坐标
                in vec3 offset;      // 实例偏移量
                in vec3 random;      // 随机值

                // 变换矩阵 (uniform 在两个版本中相同)
                uniform mat4 modelMatrix;
                uniform mat4 modelViewMatrix;
                uniform mat4 projectionMatrix;
                uniform mat3 normalMatrix;

                // 输出变量 (WebGL 2.0 使用 'out' 关键字)
                out vec2 vUv;       // 传递纹理坐标
                out vec3 vNormal;   // 传递世界空间法线
                out vec3 vMPos;     // 传递世界空间位置

                // 2D旋转函数 (与 WebGL 1.0 版本相同)
                void rotate2d(inout vec2 v, float a){
                    mat2 m = mat2(cos(a), -sin(a), sin(a),  cos(a));
                    v = m * v;
                }

                void main() {
                    // 几何变换逻辑与 WebGL 1.0 版本完全相同
                    vec3 pos = position;
                    pos *= 0.8 + random.y * 0.3;                                    // 随机缩放
                    rotate2d(pos.xz, random.x * 6.28 + 4.0 * (random.y - 0.5));   // XZ平面旋转
                    rotate2d(pos.zy, random.z * 0.9 * sin(random.x + random.z * 3.14)); // ZY平面旋转
                    pos += offset * vec3(2.5, 0.2, 2.5);                           // 实例偏移

                    // 输出数据到片段着色器
                    vUv = uv;
                    vNormal = normalize(normalMatrix * normal);
                    vMPos = (modelMatrix * vec4(pos, 1.0)).xyz;

                    gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
                }
            `;

            // WebGL 2.0 片段着色器 - 现代 MRT 实现
            // 功能：使用显式输出声明替代 gl_FragData
            const fragment300 = /* glsl */ `#version 300 es
                precision highp float;

                uniform sampler2D tMap;     // 颜色纹理

                // 输入变量 (WebGL 2.0 使用 'in' 关键字)
                in vec2 vUv;       // 纹理坐标
                in vec3 vNormal;   // 世界空间法线
                in vec3 vMPos;     // 世界空间位置

                // 显式声明输出数组 (WebGL 2.0 的 MRT 方式)
                // 这比 gl_FragData 更清晰和类型安全
                out vec4 FragData[2];

                void main() {
                    // 输出到多个渲染目标 (与 WebGL 1.0 逻辑相同)
                    FragData[0] = texture(tMap, vUv);           // 颜色信息
                    FragData[1] = vec4(vMPos, gl_FragCoord.z);  // 位置和深度信息
                }
            `;

            // ========================================
            // 后处理着色器 (全屏渲染)
            // ========================================

            // 后处理顶点着色器
            // 功能：渲染全屏三角形，用于后处理效果
            const vertexPost = /* glsl */ `
                // 简单的全屏渲染属性
                attribute vec3 position;    // 顶点位置 (NDC坐标)
                attribute vec2 uv;          // 纹理坐标

                varying vec2 vUv;           // 传递纹理坐标到片段着色器

                void main() {
                    vUv = uv;
                    // 直接使用 NDC 坐标，无需变换矩阵
                    gl_Position = vec4(position, 1.0);
                }
            `;

            // 后处理片段着色器
            // 功能：读取 MRT 数据，应用光照和效果，并显示调试信息
            const fragmentPost = /* glsl */ `
                precision highp float;

                // MRT 渲染目标纹理
                uniform sampler2D tColor;       // 颜色渲染目标 (来自 gl_FragData[0])
                uniform sampler2D tPosition;    // 位置渲染目标 (来自 gl_FragData[1])

                // 光照和动画参数
                uniform vec3 uLights[10];       // 10个光源的颜色/位置数据
                uniform float uAspect;          // 屏幕宽高比
                uniform float uTime;            // 时间 (用于动画)

                varying vec2 vUv;               // 屏幕空间纹理坐标
                varying vec3 vNormal;           // 未使用

                void main() {
                    // ========================================
                    // 第一步：从 MRT 提取数据
                    // ========================================

                    // 从颜色渲染目标读取基础颜色
                    vec3 color = texture2D(tColor, vUv).rgb;

                    // 从位置渲染目标读取世界位置和深度
                    vec4 pos = texture2D(tPosition, vUv);
                    float depth = pos.a;    // Alpha通道存储的深度值

                    // ========================================
                    // 第二步：动态光照计算
                    // ========================================

                    vec3 lights = vec3(0);  // 累积光照结果

                    // 遍历所有光源
                    for (int i = 0; i < 10; ++i) {
                        // 将Y坐标置零，使光源在地面上移动
                        pos.xyz *= vec3(1, 0, 1);
                        vec3 l = uLights[i];    // 当前光源数据

                        // 光源动画：基于时间的正弦波运动
                        // 每个光源有不同的运动轨迹和速度
                        vec3 lPos = sin(l.xyz * uTime + l.zyx * 6.28) * vec3(2, 0.5, 2);

                        // 计算光照强度：基于距离的衰减
                        float strength = max(0.0, 1.0 - length(pos.xyz - lPos));

                        // 累积光照：使用光源颜色和强度
                        // (vec3(1) - normalize(l)) 创建基于光源数据的颜色变化
                        lights = max(lights, (vec3(1) - normalize(l)) * strength);
                    }

                    // 应用光照到基础颜色
                    color *= lights;

                    // ========================================
                    // 第三步：深度雾效果
                    // ========================================

                    // 基于深度的雾效果：远处物体逐渐变黑
                    // pow(depth, 3.0) 创建非线性的深度衰减
                    // smoothstep 提供平滑的过渡
                    color = mix(color, vec3(0), smoothstep(0.8, 1.05, pow(depth, 3.0)));

                    // 输出最终颜色
                    gl_FragColor.rgb = color;
                    gl_FragColor.a = 1.0;

                    // ========================================
                    // 第四步：调试显示 (左上角小窗口)
                    // ========================================

                    // 计算调试窗口的UV坐标
                    vec2 uv = gl_FragCoord.xy / vec2(250.0 * uAspect, 250.0);

                    // 显示深度信息 (最下方窗口)
                    if (uv.y < 1.0 && uv.x < 1.0) {
                        gl_FragColor.rgb = vec3(texture2D(tPosition, mod(uv, 1.0)).a);
                    }
                    // 显示世界位置信息 (中间窗口)
                    else if (uv.y < 2.0 && uv.x < 1.0) {
                        gl_FragColor.rgb = texture2D(tPosition, mod(uv, 1.0)).rgb;
                    }
                    // 显示原始颜色信息 (最上方窗口)
                    else if (uv.y < 3.0 && uv.x < 1.0) {
                        gl_FragColor.rgb = texture2D(tColor, mod(uv, 1.0)).rgb;
                    }
                }
            `;

            // ========================================
            // 主程序：MRT 演示应用
            // ========================================
            {
                // 创建渲染器，使用2倍像素密度提高画质
                const renderer = new Renderer({ dpr: 2 });
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);

                // 设置黑色背景
                gl.clearColor(0, 0, 0, 1);

                // 创建透视相机
                const camera = new Camera(gl, { fov: 45 });
                camera.position.set(0, 2, 5); // 相机位置：稍微上方和后方

                // 添加轨道控制器，允许用户交互
                const controls = new Orbit(camera);

                // 窗口大小调整处理
                function resize() {
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize();

                // 创建场景根节点
                const scene = new Transform();

                // 初始化要渲染到 MRT 的场景
                initScene();

                // ========================================
                // MRT 渲染目标配置
                // ========================================

                // 检查是否支持半精度浮点纹理的线性过滤
                // 这对于位置数据的平滑插值很重要
                let supportLinearFiltering = gl.renderer.extensions[`OES_texture_${gl.renderer.isWebgl2 ? `` : `half_`}float_linear`];

                // 创建多渲染目标 (MRT)
                const target = new RenderTarget(gl, {
                    color: 2, // 创建2个颜色附件 (对应 gl_FragData[0] 和 gl_FragData[1])

                    // 使用半精度浮点格式存储精确的位置值
                    // WebGL 1.0 和 2.0 的格式略有不同
                    type: gl.renderer.isWebgl2 ? gl.HALF_FLOAT : gl.renderer.extensions['OES_texture_half_float'].HALF_FLOAT_OES,
                    internalFormat: gl.renderer.isWebgl2 ? gl.RGBA16F : gl.RGBA,

                    // 根据扩展支持情况选择过滤方式
                    minFilter: supportLinearFiltering ? gl.LINEAR : gl.NEAREST,
                });

                // 创建后处理网格 (用于全屏渲染)
                const post = initPost();

                // ========================================
                // 场景初始化：创建实例化渲染的橡子模型
                // ========================================
                async function initScene() {
                    // 加载橡子模型的几何数据 (JSON格式)
                    const data = await (await fetch(`assets/acorn.json`)).json();

                    // 创建实例化渲染的缓冲区
                    const num = 100; // 渲染100个橡子实例

                    // 为每个实例创建随机偏移位置
                    let offset = new Float32Array(num * 3);
                    // 为每个实例创建随机参数 (用于变换和动画)
                    let random = new Float32Array(num * 3);

                    for (let i = 0; i < num; i++) {
                        // 随机偏移：在 [-1, 1] 范围内分布
                        offset.set([Math.random() * 2 - 1, Math.random() * 2 - 1, Math.random() * 2 - 1], i * 3);
                        // 随机参数：在 [0, 1] 范围内，用于旋转和缩放
                        random.set([Math.random(), Math.random(), Math.random()], i * 3);
                    }

                    // 创建几何体，包含基础数据和实例化数据
                    const geometry = new Geometry(gl, {
                        // 基础几何数据 (所有实例共享)
                        position: { size: 3, data: new Float32Array(data.position) }, // 顶点位置
                        uv: { size: 2, data: new Float32Array(data.uv) }, // 纹理坐标
                        normal: { size: 3, data: new Float32Array(data.normal) }, // 顶点法线

                        // 实例化数据 (每个实例不同)
                        offset: { instanced: 1, size: 3, data: offset }, // 实例偏移
                        random: { instanced: 1, size: 3, data: random }, // 实例随机参数
                    });

                    // 创建橡子纹理
                    const texture = new Texture(gl);
                    const img = new Image();
                    img.onload = () => (texture.image = img);
                    img.src = 'assets/acorn.jpg';

                    // 创建着色器程序
                    // 根据 WebGL 版本选择对应的着色器
                    const program = new Program(gl, {
                        vertex: renderer.isWebgl2 ? vertex300 : vertex100, // 顶点着色器
                        fragment: renderer.isWebgl2 ? fragment300 : fragment100, // 片段着色器 (MRT)
                        uniforms: {
                            tMap: { value: texture }, // 橡子纹理
                        },
                    });

                    // 创建网格并添加到场景
                    const mesh = new Mesh(gl, { geometry, program });
                    mesh.setParent(scene);
                }

                // ========================================
                // 后处理初始化：创建全屏渲染网格
                // ========================================
                function initPost() {
                    // 使用全屏三角形几何体 (比四边形更高效)
                    const geometry = new Triangle(gl);

                    // 创建10个随机光源
                    // 每个光源的颜色/位置数据用于后续的动画和光照计算
                    const lights = [];
                    for (let i = 0; i < 10; i++) {
                        // 随机生成光源参数：在 [-1, 1] 范围内
                        lights.push(new Vec3(Math.random() * 2 - 1, Math.random() * 2 - 1, Math.random() * 2 - 1));
                    }

                    // 创建后处理着色器程序
                    const program = new Program(gl, {
                        vertex: vertexPost, // 简单的全屏顶点着色器
                        fragment: fragmentPost, // 复杂的后处理片段着色器
                        uniforms: {
                            // MRT 纹理输入
                            tColor: { value: target.textures[0] }, // 颜色渲染目标
                            tPosition: { value: target.textures[1] }, // 位置渲染目标

                            // 光照和动画参数
                            uLights: { value: lights }, // 光源数组
                            uAspect: { value: 1 }, // 屏幕宽高比
                            uTime: { value: 0 }, // 动画时间
                        },
                    });

                    return new Mesh(gl, { geometry, program });
                }

                // ========================================
                // 渲染循环：MRT 渲染管线
                // ========================================
                requestAnimationFrame(update);
                function update(t) {
                    requestAnimationFrame(update);

                    // 更新相机控制器
                    controls.update();

                    // 第一步：渲染场景到 MRT
                    // 这会同时生成颜色和位置数据到两个渲染目标
                    renderer.render({ scene, camera, target });

                    // 更新后处理着色器的参数
                    post.program.uniforms.uAspect.value = renderer.width / renderer.height; // 屏幕宽高比
                    post.program.uniforms.uTime.value = t * 0.001; // 时间 (毫秒转秒)

                    // 第二步：后处理渲染到屏幕
                    // 读取 MRT 数据，应用光照效果，并显示最终结果
                    renderer.render({ scene: post });
                }
            }
        </script>
    </body>
</html>
