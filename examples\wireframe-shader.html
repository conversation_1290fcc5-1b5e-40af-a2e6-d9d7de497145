<!DOCTYPE html>
<html lang="en">
    <head>
        <!-- 设置文档字符编码为UTF-8 -->
        <meta charset="UTF-8" />
        <!-- 设置IE浏览器兼容性，使用最新的渲染引擎 -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <!-- 移动端视口设置：禁用缩放，适配全屏显示 -->
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <!-- 设置网站图标 -->
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <!-- 页面标题：OGL线框着色器效果演示 -->
        <title>OGL • Wireframe Shader</title>
        <!-- 引入主样式表 -->
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <!-- 页面信息提示：展示线框着色器效果，模型来自Google Poly -->
        <div class="Info">Wireframe Shader. Model by Google Poly</div>
        <script type="module">
            // 从OGL库导入所需的WebGL组件
            // Renderer: 渲染器，负责WebGL上下文管理和渲染
            // Camera: 相机，定义视角和投影
            // Transform: 变换节点，用于场景图管理
            // Program: 着色器程序，包含顶点和片段着色器
            // Geometry: 几何体，定义顶点数据
            // Mesh: 网格，结合几何体和着色器程序
            // Vec3: 三维向量，用于位置和方向计算
            // Orbit: 轨道控制器，实现相机交互控制
            import { Renderer, Camera, Transform, Program, Geometry, Mesh, Vec3, Orbit } from '../src/index.js';

            // WebGL1版本的顶点着色器（GLSL 100 ES）
            // 用于线框渲染的顶点着色器，关键是传递重心坐标
            const vertex100 = /* glsl */ `
                // 输入：顶点位置属性（来自几何体的position数据）
                attribute vec3 position;
                // 输入：重心坐标属性（用于线框效果计算）
                // 重心坐标是三角形内任意点相对于三个顶点的权重坐标
                attribute vec3 barycentric;

                // 统一变量：模型视图矩阵（将模型坐标转换为视图坐标）
                uniform mat4 modelViewMatrix;
                // 统一变量：投影矩阵（将视图坐标转换为裁剪坐标）
                uniform mat4 projectionMatrix;

                // 输出：重心坐标（传递给片段着色器用于线框计算）
                varying vec3 vBarycentric;

                void main() {
                    // 将重心坐标传递给片段着色器
                    // 这是线框渲染的关键数据
                    vBarycentric = barycentric;

                    // 计算最终的裁剪空间位置
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // WebGL1版本的片段着色器（GLSL 100 ES）
            // 实现线框渲染效果的核心着色器
            const fragment100 = /* glsl */ `#extension GL_OES_standard_derivatives : enable
                precision highp float;

                // 输入：从顶点着色器传递的重心坐标
                varying vec3 vBarycentric;

                void main() {
                    // 获取当前像素的重心坐标
                    vec3 bary = vBarycentric;

                    // 初始化颜色和透明度
                    vec3 color = vec3(0);  // 黑色线框
                    float alpha = 1.0;     // 完全不透明

                    // 线框宽度控制（以像素为单位）
                    float width = 1.0 * 0.5;  // 0.5像素宽度

                    // 使用fwidth计算重心坐标的屏幕空间导数
                    // fwidth = abs(dFdx) + abs(dFdy)，用于抗锯齿
                    vec3 d = fwidth(bary);

                    // 使用smoothstep创建平滑的线框边缘
                    // 当重心坐标接近0时（靠近三角形边缘），smoothstep返回较小值
                    vec3 s = smoothstep(d * (width + 0.5), d * (width - 0.5), bary);

                    // 取三个重心坐标分量的最大值作为最终的alpha
                    // 这样只有在三角形边缘附近alpha才会较小，形成线框效果
                    alpha *= max(max(s.x, s.y), s.z);

                    // 虚线效果：使用正弦函数创建周期性的透明度变化
                    // max(bary.x, bary.y)确保在边缘附近有虚线效果
                    // 乘以3.14 * 5.0控制虚线的频率
                    alpha *= step(0.0, sin(max(bary.x, bary.y) * 3.14 * 5.0));

                    // 背面着色：区分正面和背面
                    // gl_FrontFacing为true时是正面，false时是背面
                    // 背面显示为红色，正面保持黑色
                    color = mix(vec3(1, 0, 0), color, vec3(gl_FrontFacing));

                    // 背面透明度调整：背面更透明，正面保持原透明度
                    alpha = mix(alpha * 0.1 + 0.02, alpha, float(gl_FrontFacing));

                    // 输出最终颜色
                    gl_FragColor.rgb = color;
                    gl_FragColor.a = alpha;
                }
            `;

            // WebGL2版本的顶点着色器（GLSL 300 ES）
            // 功能与WebGL1版本相同，但使用新的GLSL语法
            const vertex300 = /* glsl */ `#version 300 es
                // 输入：顶点位置属性（WebGL2中attribute改为in）
                in vec3 position;
                // 输入：重心坐标属性
                in vec3 barycentric;

                // 统一变量：模型视图矩阵
                uniform mat4 modelViewMatrix;
                // 统一变量：投影矩阵
                uniform mat4 projectionMatrix;

                // 输出：重心坐标（WebGL2中varying改为out）
                out vec3 vBarycentric;

                void main() {
                    // 传递重心坐标到片段着色器
                    vBarycentric = barycentric;

                    // 计算最终的裁剪空间位置
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // WebGL2版本的片段着色器（GLSL 300 ES）
            // 线框渲染逻辑与WebGL1版本完全相同，只是语法不同
            const fragment300 = /* glsl */ `#version 300 es
                precision highp float;

                // 输入：从顶点着色器传递的重心坐标（WebGL2中varying改为in）
                in vec3 vBarycentric;

                // 输出：片段颜色（WebGL2中gl_FragColor改为自定义输出变量）
                out vec4 FragColor;

                void main() {
                    // 获取当前像素的重心坐标
                    vec3 bary = vBarycentric;

                    // 初始化颜色和透明度
                    vec3 color = vec3(0);  // 黑色线框
                    float alpha = 1.0;     // 完全不透明

                    // 线框宽度控制（以像素为单位）
                    float width = 1.0 * 0.5;  // 0.5像素宽度

                    // 计算重心坐标的屏幕空间导数，用于抗锯齿
                    vec3 d = fwidth(bary);

                    // 创建平滑的线框边缘
                    vec3 s = smoothstep(d * (width + 0.5), d * (width - 0.5), bary);

                    // 计算线框的透明度
                    alpha *= max(max(s.x, s.y), s.z);

                    // 虚线效果：创建周期性的透明度变化
                    alpha *= step(0.0, sin(max(bary.x, bary.y) * 3.14 * 5.0));

                    // 背面着色：背面显示红色，正面显示黑色
                    color = mix(vec3(1, 0, 0), color, vec3(gl_FrontFacing));

                    // 背面透明度调整：背面更透明
                    alpha = mix(alpha * 0.1 + 0.02, alpha, float(gl_FrontFacing));

                    // 输出最终颜色
                    FragColor.rgb = color;
                    FragColor.a = alpha;
                }
            `;

            {
                // 创建WebGL渲染器
                // dpr: 2 设置设备像素比为2，提供高分辨率渲染（适配高DPI屏幕）
                const renderer = new Renderer({ dpr: 2 });

                // 获取WebGL上下文
                const gl = renderer.gl;

                // 将canvas元素添加到页面body中
                document.body.appendChild(gl.canvas);

                // 设置清除颜色为白色（RGBA: 1,1,1,1）
                // 白色背景能更好地突出黑色线框效果
                gl.clearColor(1, 1, 1, 1);

                // 创建透视相机
                // fov: 35 设置视野角度为35度（比平面着色示例更窄，更适合观察细节）
                const camera = new Camera(gl, { fov: 35 });

                // 设置相机位置：x=3, y=2, z=4
                // 这个位置让相机从右上前方观察模型，便于观察线框效果
                camera.position.set(3, 2, 4);

                // 创建轨道控制器，实现鼠标交互控制相机
                const controls = new Orbit(camera, {
                    // 设置相机注视目标点：稍微偏上的位置(0, 1, 0)
                    // 适合观察山羊模型的整体结构
                    target: new Vec3(0, 1, 0),
                });

                // 窗口大小调整处理函数
                function resize() {
                    // 更新渲染器尺寸为窗口大小
                    renderer.setSize(window.innerWidth, window.innerHeight);

                    // 更新相机投影矩阵的宽高比
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }

                // 监听窗口大小变化事件
                window.addEventListener('resize', resize, false);

                // 初始化时调用一次resize，设置正确的画布尺寸
                resize();

                // 创建场景根节点
                const scene = new Transform();

                // 创建线框着色器程序
                const program = new Program(gl, {
                    // 根据WebGL版本选择对应的顶点着色器
                    vertex: renderer.isWebgl2 ? vertex300 : vertex100,

                    // 根据WebGL版本选择对应的片段着色器
                    fragment: renderer.isWebgl2 ? fragment300 : fragment100,

                    // 启用透明度混合，支持半透明线框效果
                    transparent: true,

                    // 禁用面剔除，确保背面也能被渲染（用于背面着色效果）
                    cullFace: false,

                    // 禁用深度测试，避免线框被遮挡
                    // 这样可以看到模型内部的线框结构
                    depthTest: false,
                });

                // 重心坐标生成函数
                // 线框渲染的核心：为每个三角形的顶点分配重心坐标
                function addBarycentric(position) {
                    // 计算三角形数量：每个三角形有3个顶点，每个顶点有3个坐标分量
                    const count = position.length / 9;
                    const barycentric = [];

                    // 为每个三角形分配重心坐标
                    for (let i = 0; i < count; i++) {
                        // 交替使用两种重心坐标模式，避免相邻三角形的线框重叠
                        if (i % 2 === 0) {
                            // 第一种模式：
                            // 顶点1: (0,0,1) - 在第三条边上
                            // 顶点2: (0,1,0) - 在第二条边上
                            // 顶点3: (1,0,0) - 在第一条边上
                            barycentric.push(0, 0, 1, 0, 1, 0, 1, 0, 0);
                        } else {
                            // 第二种模式：不同的重心坐标分配
                            // 这样可以在相邻三角形之间创建不同的线框模式
                            barycentric.push(0, 1, 0, 0, 0, 1, 1, 0, 0);
                        }
                    }

                    // 返回Float32Array格式的重心坐标数据
                    return new Float32Array(barycentric);
                }

                // 网格对象变量，用于动画旋转
                let mesh;

                // 调用模型加载函数
                loadModel();

                // 异步加载3D模型数据
                async function loadModel() {
                    // 从JSON文件加载山羊模型的顶点数据
                    const data = await (await fetch(`assets/goat.json`)).json();

                    // 创建几何体，包含完整的顶点数据
                    const geometry = new Geometry(gl, {
                        // 顶点位置数据
                        position: { size: 3, data: new Float32Array(data.position) },

                        // UV纹理坐标（虽然线框渲染不需要，但保留完整数据）
                        uv: { size: 2, data: new Float32Array(data.uv) },

                        // 法线数据（线框渲染不需要，但保留完整数据）
                        normal: { size: 3, data: new Float32Array(data.normal) },

                        // 重心坐标数据（线框渲染的关键数据）
                        // 使用自定义函数生成，每个顶点3个分量
                        barycentric: { size: 3, data: addBarycentric(data.position) },
                    });

                    // 创建网格对象，结合几何体和线框着色器程序
                    mesh = new Mesh(gl, { geometry, program });

                    // 将网格添加到场景中
                    mesh.setParent(scene);
                }

                // 启动渲染循环
                requestAnimationFrame(update);

                // 渲染循环函数
                function update() {
                    // 请求下一帧动画，形成连续的渲染循环
                    requestAnimationFrame(update);

                    // 如果模型已加载，添加自动旋转动画
                    // 绕Y轴缓慢旋转，便于观察线框效果的各个角度
                    if (mesh) mesh.rotation.y += 0.005;

                    // 更新轨道控制器（处理鼠标交互）
                    controls.update();

                    // 执行渲染：将场景通过相机渲染到画布
                    // 线框效果将在片段着色器中基于重心坐标计算
                    renderer.render({ scene, camera });
                }
            }
        </script>
    </body>
</html>
