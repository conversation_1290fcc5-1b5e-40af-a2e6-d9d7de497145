<!DOCTYPE html>
<html lang="en">
    <head>
        <!-- 设置文档字符编码为UTF-8 -->
        <meta charset="UTF-8" />
        <!-- 设置IE浏览器兼容性，使用最新的渲染引擎 -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <!-- 移动端视口设置：禁用缩放，适配全屏显示 -->
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <!-- 设置网站图标 -->
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <!-- 页面标题：OGL基于物理的渲染（PBR）演示 -->
        <title>OGL • PBR</title>
        <!-- 引入主样式表 -->
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <!-- 页面信息提示：展示PBR渲染效果，模型来自Slava Z -->
        <div class="Info" style="color: #fff">PBR (Physically Based Rendering). Model by <a href="https://sketchfab.com/slava" target="_blank">Slava Z</a></div>
        <script type="module">
            // 从OGL库导入PBR渲染所需的核心组件
            // Renderer: 渲染器，负责WebGL上下文管理
            // Transform: 变换节点，用于场景图管理
            // Camera: 相机，定义视角和投影
            // Geometry: 几何体，定义顶点数据
            // Texture: 纹理，用于材质贴图
            // Program: 着色器程序，包含PBR着色器
            // Mesh: 网格，结合几何体和材质
            // Vec3: 三维向量，用于位置、颜色和方向
            // Color: 颜色类，用于颜色管理
            // Orbit: 轨道控制器，实现相机交互
            // Plane: 平面几何体，用于阴影渲染
            import { Renderer, Transform, Camera, Geometry, Texture, Program, Mesh, Vec3, Color, Orbit, Plane } from '../src/index.js';

            // WebGL1版本的PBR顶点着色器（GLSL 100 ES）
            // PBR渲染需要传递多种数据到片段着色器进行复杂的光照计算
            const vertex100 = /* glsl */ `
                // 输入：顶点位置属性（来自几何体的position数据）
                attribute vec3 position;
                // 输入：UV纹理坐标（用于采样各种PBR贴图）
                attribute vec2 uv;
                // 输入：顶点法线（用于光照计算的基础法线）
                attribute vec3 normal;

                // 统一变量：法线矩阵（将法线从模型空间转换到视图空间）
                uniform mat3 normalMatrix;
                // 统一变量：模型矩阵（将顶点从模型空间转换到世界空间）
                uniform mat4 modelMatrix;
                // 统一变量：模型视图矩阵（模型空间到视图空间的组合变换）
                uniform mat4 modelViewMatrix;
                // 统一变量：投影矩阵（视图空间到裁剪空间的变换）
                uniform mat4 projectionMatrix;

                // 输出：UV坐标（传递给片段着色器用于纹理采样）
                varying vec2 vUv;
                // 输出：视图空间法线（用于光照计算）
                varying vec3 vNormal;
                // 输出：世界空间位置（用于计算视线方向和反射）
                varying vec3 vMPos;

                void main() {
                    // 直接传递UV坐标到片段着色器
                    vUv = uv;

                    // 将法线转换到视图空间并归一化
                    // 这是PBR光照计算的基础
                    vNormal = normalize(normalMatrix * normal);

                    // 计算世界空间位置，用于计算视线方向
                    // 这对于菲涅尔反射和环境映射至关重要
                    vMPos = (modelMatrix * vec4(position, 1.0)).xyz;

                    // 计算最终的裁剪空间位置
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // WebGL1版本的PBR片段着色器（GLSL 100 ES）
            // 这是一个完整的基于物理的渲染着色器，实现了现代PBR工作流
            const fragment100 = /* glsl */ `
                // 启用标准导数扩展，用于法线贴图的切线空间计算
                #extension GL_OES_standard_derivatives : enable

                // 设置高精度浮点数，确保PBR计算的精确性
                precision highp float;

                // 相机相关统一变量
                uniform vec3 cameraPosition;  // 世界空间中的相机位置，用于计算视线方向
                uniform mat4 viewMatrix;      // 视图矩阵，用于坐标空间转换

                // 基础颜色/反照率贴图和参数
                uniform sampler2D tBaseColor; // 基础颜色贴图（反照率贴图）
                uniform vec3 uBaseColor;      // 基础颜色倍增器，用于调整整体颜色
                uniform float uAlpha;         // 整体透明度控制

                // RMO贴图：Roughness(粗糙度)、Metallic(金属度)、Occlusion(环境遮蔽)
                uniform sampler2D tRMO;       // RMO打包贴图：R=粗糙度，G=金属度，B=环境遮蔽
                uniform float uMetallic;      // 金属度倍增器（0=电介质，1=金属）
                uniform float uRoughness;     // 粗糙度倍增器（0=镜面，1=完全粗糙）
                uniform float uOcclusion;     // 环境遮蔽强度控制

                // 法线贴图相关参数
                uniform sampler2D tNormal;    // 法线贴图（切线空间）
                uniform float uNormalScale;   // 法线强度缩放因子
                uniform float uNormalUVScale; // 法线贴图UV缩放（用于细节调整）

                // 自发光贴图
                uniform sampler2D tEmissive;  // 自发光贴图，模拟发光材质
                uniform float uEmissive;      // 自发光强度倍增器

                // 透明度贴图
                uniform sampler2D tOpacity;   // 透明度贴图，绿色通道存储透明度信息

                // IBL（基于图像的光照）相关贴图
                uniform sampler2D tLUT;       // BRDF查找表，用于优化菲涅尔计算
                uniform sampler2D tEnvDiffuse;  // 环境漫反射贴图（辐照度贴图）
                uniform sampler2D tEnvSpecular; // 环境镜面反射贴图（预过滤环境贴图）
                uniform float uEnvSpecular;   // 环境镜面反射强度控制

                // 直接光照参数
                uniform vec3 uLightDirection; // 主光源方向（通常模拟太阳光）
                uniform vec3 uLightColor;     // 主光源颜色和强度

                // 从顶点着色器传入的变量
                varying vec2 vUv;     // UV纹理坐标
                varying vec3 vNormal; // 视图空间法线
                varying vec3 vMPos;   // 世界空间位置

                // PBR渲染中使用的数学常量
                const float PI = 3.14159265359;           // 圆周率，用于BRDF计算
                const float RECIPROCAL_PI = 0.31830988618; // 1/π，用于漫反射归一化
                const float RECIPROCAL_PI2 = 0.15915494;   // 1/(2π)，用于球面坐标转换
                const float LN2 = 0.6931472;              // ln(2)，用于mipmap级别计算

                // 环境贴图的mipmap级别数量（用于不同粗糙度的镜面反射）
                const float ENV_LODS = 6.0;

                // 颜色空间转换函数：sRGB到线性空间
                // PBR计算必须在线性空间进行，而纹理通常存储在sRGB空间
                vec4 SRGBtoLinear(vec4 srgb) {
                    // 使用gamma 2.2进行近似转换（标准是2.4，但2.2更常用）
                    vec3 linOut = pow(srgb.xyz, vec3(2.2));
                    return vec4(linOut, srgb.w);;
                }

                // RGBM格式解码：将RGBM编码的HDR数据转换为线性HDR
                // RGBM格式允许在8位纹理中存储HDR数据
                vec4 RGBMToLinear(in vec4 value) {
                    float maxRange = 6.0; // HDR范围倍数，最大亮度可达6倍
                    // RGB通道乘以Alpha通道再乘以范围倍数，恢复原始HDR值
                    return vec4(value.xyz * value.w * maxRange, 1.0);
                }

                // 颜色空间转换函数：线性空间到sRGB
                // 最终输出需要转换回sRGB空间以正确显示
                vec3 linearToSRGB(vec3 color) {
                    // 使用gamma 1/2.2进行逆转换
                    return pow(color, vec3(1.0 / 2.2));
                }

                // 计算最终的世界空间法线（结合顶点法线和法线贴图）
                // 这是PBR渲染中最复杂的部分之一，需要构建切线空间基
                vec3 getNormal() {
                    // 使用屏幕空间导数计算世界空间位置的变化率
                    vec3 pos_dx = dFdx(vMPos.xyz); // 世界位置在x方向的变化
                    vec3 pos_dy = dFdy(vMPos.xyz); // 世界位置在y方向的变化

                    // 计算UV坐标的屏幕空间导数
                    vec2 tex_dx = dFdx(vUv); // UV坐标在x方向的变化
                    vec2 tex_dy = dFdy(vUv); // UV坐标在y方向的变化

                    // 构建切线空间基向量（TBN矩阵）
                    // 使用屏幕空间导数动态计算切线和副切线
                    vec3 t = normalize(pos_dx * tex_dy.t - pos_dy * tex_dx.t); // 切线向量T
                    vec3 b = normalize(-pos_dx * tex_dy.s + pos_dy * tex_dx.s); // 副切线向量B
                    mat3 tbn = mat3(t, b, normalize(vNormal)); // TBN矩阵：切线空间到视图空间

                    // 从法线贴图采样切线空间法线
                    vec3 n = texture2D(tNormal, vUv * uNormalUVScale).rgb * 2.0 - 1.0;
                    // 应用法线强度缩放（只影响XY分量，保持Z分量）
                    n.xy *= uNormalScale;

                    // 将切线空间法线转换到视图空间
                    vec3 normal = normalize(tbn * n);

                    // 将视图空间法线转换到世界空间
                    // 这对于环境映射和IBL计算是必需的
                    return normalize((vec4(normal, 0.0) * viewMatrix).xyz);
                }

                // 菲涅尔反射函数（Schlick近似）
                // 计算不同角度下的镜面反射强度，这是PBR的核心组件之一
                vec3 specularReflection(vec3 specularEnvR0, vec3 specularEnvR90, float VdH) {
                    // Schlick菲涅尔近似：F = F0 + (F90 - F0) * (1 - VdH)^5
                    // F0: 垂直入射时的反射率，F90: 掠射角时的反射率
                    // VdH: 视线方向与半角向量的点积
                    return specularEnvR0 + (specularEnvR90 - specularEnvR0) * pow(clamp(1.0 - VdH, 0.0, 1.0), 5.0);
                }

                // 几何遮蔽函数（Smith G函数）
                // 计算微表面的自遮蔽效应，粗糙表面会产生更多的自遮蔽
                float geometricOcclusion(float NdL, float NdV, float roughness) {
                    float r = roughness;

                    // 计算光线方向的遮蔽衰减
                    // 使用Smith G1函数的近似形式
                    float attenuationL = 2.0 * NdL / (NdL + sqrt(r * r + (1.0 - r * r) * (NdL * NdL)));

                    // 计算视线方向的遮蔽衰减
                    float attenuationV = 2.0 * NdV / (NdV + sqrt(r * r + (1.0 - r * r) * (NdV * NdV)));

                    // 返回双向遮蔽函数G = G1(L) * G1(V)
                    return attenuationL * attenuationV;
                }

                // 微表面分布函数（GGX/Trowbridge-Reitz分布）
                // 描述微表面法线的统计分布，决定了高光的形状和大小
                float microfacetDistribution(float roughness, float NdH) {
                    float roughnessSq = roughness * roughness; // α² = roughness²

                    // GGX分布公式：D = α² / (π * ((NdH² * (α² - 1) + 1)²))
                    float f = (NdH * roughnessSq - NdH) * NdH + 1.0; // NdH² * (α² - 1) + 1
                    return roughnessSq / (PI * f * f);
                }

                // 笛卡尔坐标到球面坐标转换
                // 将3D方向向量转换为2D UV坐标，用于采样等距柱状投影的环境贴图
                vec2 cartesianToPolar(vec3 n) {
                    vec2 uv;
                    // 计算方位角：atan2(z, x)，范围[-π, π]，转换到[0, 1]
                    uv.x = atan(n.z, n.x) * RECIPROCAL_PI2 + 0.5;
                    // 计算仰角：asin(y)，范围[-π/2, π/2]，转换到[0, 1]
                    uv.y = asin(n.y) * RECIPROCAL_PI + 0.5;
                    return uv;
                }

                // 计算基于图像的光照（IBL）贡献
                // 这是现代PBR渲染的核心，使用环境贴图提供真实的光照效果
                void getIBLContribution(inout vec3 diffuse, inout vec3 specular, float NdV, float roughness, vec3 n, vec3 reflection, vec3 diffuseColor, vec3 specularColor) {
                    // 从BRDF查找表获取预计算的菲涅尔和几何项
                    // LUT基于视线角度(NdV)和粗糙度进行索引
                    vec3 brdf = SRGBtoLinear(texture2D(tLUT, vec2(NdV, roughness))).rgb;

                    // 采样漫反射环境光（辐照度贴图）
                    // 使用表面法线方向采样预卷积的漫反射环境光
                    vec3 diffuseLight = RGBMToLinear(texture2D(tEnvDiffuse, cartesianToPolar(n))).rgb;

                    // 计算镜面反射的mipmap级别混合
                    // 粗糙度越高，使用越模糊的环境贴图级别
                    float blend = roughness * ENV_LODS;  // 将粗糙度映射到mipmap级别
                    float level0 = floor(blend);         // 下层级别
                    float level1 = min(ENV_LODS, level0 + 1.0); // 上层级别
                    blend -= level0;                     // 混合权重

                    // 计算镜面反射方向的UV坐标
                    vec2 uvSpec = cartesianToPolar(reflection);
                    uvSpec.y /= 2.0; // 镜面贴图使用atlas的上半部分

                    // 为两个mipmap级别计算不同的UV坐标
                    vec2 uv0 = uvSpec;
                    vec2 uv1 = uvSpec;

                    // 第一个级别：根据mipmap级别缩放UV并调整Y偏移
                    uv0 /= pow(2.0, level0);                    // 缩放UV以匹配mipmap大小
                    uv0.y += 1.0 - exp(-LN2 * level0);         // 计算atlas中的Y偏移

                    // 第二个级别：同样的计算
                    uv1 /= pow(2.0, level1);
                    uv1.y += 1.0 - exp(-LN2 * level1);

                    // 从两个mipmap级别采样镜面环境光
                    vec3 specular0 = RGBMToLinear(texture2D(tEnvSpecular, uv0)).rgb;
                    vec3 specular1 = RGBMToLinear(texture2D(tEnvSpecular, uv1)).rgb;

                    // 在两个级别之间进行线性插值，获得平滑的过渡
                    vec3 specularLight = mix(specular0, specular1, blend);

                    // 计算最终的漫反射贡献
                    diffuse = diffuseLight * diffuseColor;

                    // 计算镜面反射贡献
                    // 为光滑材质添加额外的反射率，增强视觉效果
                    float reflectivity = pow((1.0 - roughness), 2.0) * 0.05;

                    // 应用分离和的BRDF近似：∫BRDF ≈ F0*scale + bias + reflectivity
                    specular = specularLight * (specularColor * brdf.x + brdf.y + reflectivity);

                    // 应用环境镜面反射强度控制
                    specular *= uEnvSpecular;
                }

                // PBR片段着色器主函数
                // 实现完整的基于物理的渲染管线
                void main() {
                    // 1. 采样并转换基础颜色到线性空间
                    vec3 baseColor = SRGBtoLinear(texture2D(tBaseColor, vUv)).rgb * uBaseColor;

                    // 2. 采样材质属性贴图（RMO打包格式）
                    // RMO贴图打包格式：R=粗糙度，G=金属度，B=环境遮蔽
                    vec4 rmaSample = texture2D(tRMO, vUv);

                    // 限制粗糙度和金属度的最小值，避免数值不稳定
                    float roughness = clamp(rmaSample.r * uRoughness, 0.04, 1.0);
                    float metallic = clamp(rmaSample.g * uMetallic, 0.04, 1.0);

                    // 3. 计算PBR材质参数
                    // F0：垂直入射时的反射率，电介质约为0.04
                    vec3 f0 = vec3(0.04);

                    // 漫反射颜色：非金属材质保留颜色，金属材质漫反射为0
                    vec3 diffuseColor = baseColor * (vec3(1.0) - f0) * (1.0 - metallic);

                    // 镜面反射颜色：电介质使用F0，金属使用基础颜色
                    vec3 specularColor = mix(f0, baseColor, metallic);

                    // 4. 计算菲涅尔反射参数
                    vec3 specularEnvR0 = specularColor;  // 垂直入射时的镜面反射率
                    // 掠射角时的镜面反射率（通常接近1.0）
                    vec3 specularEnvR90 = vec3(clamp(max(max(specularColor.r, specularColor.g), specularColor.b) * 25.0, 0.0, 1.0));

                    // 5. 计算光照所需的向量
                    vec3 N = getNormal();                           // 表面法线
                    vec3 V = normalize(cameraPosition - vMPos);     // 视线方向
                    vec3 L = normalize(uLightDirection);            // 光线方向
                    vec3 H = normalize(L + V);                      // 半角向量
                    vec3 reflection = normalize(reflect(-V, N));    // 反射方向

                    // 6. 计算各种点积（用于BRDF计算）
                    float NdL = clamp(dot(N, L), 0.001, 1.0);  // 法线与光线夹角
                    float NdV = clamp(abs(dot(N, V)), 0.001, 1.0); // 法线与视线夹角
                    float NdH = clamp(dot(N, H), 0.0, 1.0);    // 法线与半角向量夹角
                    float LdH = clamp(dot(L, H), 0.0, 1.0);    // 光线与半角向量夹角
                    float VdH = clamp(dot(V, H), 0.0, 1.0);    // 视线与半角向量夹角

                    // 7. 计算BRDF的三个核心组件
                    vec3 F = specularReflection(specularEnvR0, specularEnvR90, VdH);  // 菲涅尔项
                    float G = geometricOcclusion(NdL, NdV, roughness);                // 几何遮蔽项
                    float D = microfacetDistribution(roughness, NdH);                 // 微表面分布项

                    // 8. 计算直接光照的漫反射和镜面反射贡献
                    // 漫反射：兰伯特模型，考虑菲涅尔效应
                    vec3 diffuseContrib = (1.0 - F) * (diffuseColor / PI);

                    // 镜面反射：Cook-Torrance BRDF = F * G * D / (4 * NdL * NdV)
                    vec3 specContrib = F * G * D / (4.0 * NdL * NdV);

                    // 9. 计算直接光照的最终颜色
                    // 乘以光照强度和兰伯特余弦定律(NdL)
                    vec3 color = NdL * uLightColor * (diffuseContrib + specContrib);

                    // 10. 处理透明度
                    // 从透明度贴图的绿色通道获取基础透明度
                    float alpha = 1.0;
                    alpha *= texture2D(tOpacity, vUv).g;

                    // 为透明表面（如玻璃）添加镜面反射到透明度
                    // 这样可以在透明区域显示反射效果
                    alpha = max(alpha, max(max(specContrib.r, specContrib.g), specContrib.b));

                    // 11. 计算IBL（基于图像的光照）
                    vec3 diffuseIBL;
                    vec3 specularIBL;
                    getIBLContribution(diffuseIBL, specularIBL, NdV, roughness, N, reflection, diffuseColor, specularColor);

                    // 12. 将IBL添加到直接光照结果上
                    color += diffuseIBL + specularIBL;

                    // 同样为IBL的镜面反射添加到透明度
                    alpha = max(alpha, max(max(specularIBL.r, specularIBL.g), specularIBL.b));

                    // 13. 应用环境遮蔽
                    // 使用线性插值混合遮蔽和非遮蔽的颜色
                    color = mix(color, color * rmaSample.b, uOcclusion);

                    // 14. 添加自发光
                    // 自发光在最后添加，不受光照影响
                    vec3 emissive = SRGBtoLinear(texture2D(tEmissive, vUv)).rgb * uEmissive;
                    color += emissive;

                    // 15. 转换回sRGB空间用于显示
                    gl_FragColor.rgb = linearToSRGB(color);

                    // 16. 应用最终的透明度控制
                    // uAlpha会覆盖镜面反射对透明度的影响
                    gl_FragColor.a = alpha * uAlpha;
                }
            `;

            // WebGL2版本的PBR顶点着色器（GLSL 300 ES）
            // 功能与WebGL1版本完全相同，只是使用了新的GLSL语法
            const vertex300 = /* glsl */ `#version 300 es
                // 输入：使用'in'关键字替代'attribute'
                in vec3 position;  // 顶点位置
                in vec2 uv;        // UV纹理坐标
                in vec3 normal;    // 顶点法线

                // 统一变量保持不变
                uniform mat3 normalMatrix;
                uniform mat4 modelMatrix;
                uniform mat4 modelViewMatrix;
                uniform mat4 projectionMatrix;

                // 输出：使用'out'关键字替代'varying'
                out vec2 vUv;      // 传递UV坐标到片段着色器
                out vec3 vNormal;  // 传递视图空间法线
                out vec3 vMPos;    // 传递世界空间位置

                void main() {
                    // 逻辑与WebGL1版本完全相同
                    vUv = uv;
                    vNormal = normalize(normalMatrix * normal);
                    vMPos = (modelMatrix * vec4(position, 1.0)).xyz;

                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // WebGL2版本的PBR片段着色器（GLSL 300 ES）
            // 功能与WebGL1版本完全相同，主要差异在于语法和纹理采样函数
            const fragment300 = /* glsl */ `#version 300 es
                precision highp float;

                // 统一变量与WebGL1版本完全相同
                uniform vec3 cameraPosition;
                uniform mat4 viewMatrix;

                uniform sampler2D tBaseColor;
                uniform vec3 uBaseColor;
                uniform float uAlpha;

                uniform sampler2D tRMO;
                uniform float uMetallic;
                uniform float uRoughness;
                uniform float uOcclusion;

                uniform sampler2D tNormal;
                uniform float uNormalScale;
                uniform float uNormalUVScale;

                uniform sampler2D tEmissive;
                uniform float uEmissive;

                uniform sampler2D tOpacity;

                uniform sampler2D tLUT;
                uniform sampler2D tEnvDiffuse;
                uniform sampler2D tEnvSpecular;
                uniform float uEnvSpecular;

                uniform vec3 uLightDirection;
                uniform vec3 uLightColor;

                // 输入：使用'in'关键字替代'varying'
                in vec2 vUv;
                in vec3 vNormal;
                in vec3 vMPos;

                // 输出：使用自定义输出变量替代'gl_FragColor'
                out vec4 FragColor;

                const float PI = 3.14159265359;
                const float RECIPROCAL_PI = 0.31830988618;
                const float RECIPROCAL_PI2 = 0.15915494;
                const float LN2 = 0.6931472;

                const float ENV_LODS = 6.0;

                vec4 SRGBtoLinear(vec4 srgb) {
                    vec3 linOut = pow(srgb.xyz, vec3(2.2));
                    return vec4(linOut, srgb.w);;
                }

                vec4 RGBMToLinear(in vec4 value) {
                    float maxRange = 6.0;
                    return vec4(value.xyz * value.w * maxRange, 1.0);
                }

                vec3 linearToSRGB(vec3 color) {
                    return pow(color, vec3(1.0 / 2.2));
                }

                vec3 getNormal() {
                    vec3 pos_dx = dFdx(vMPos.xyz);
                    vec3 pos_dy = dFdy(vMPos.xyz);
                    vec2 tex_dx = dFdx(vUv);
                    vec2 tex_dy = dFdy(vUv);

                    vec3 t = normalize(pos_dx * tex_dy.t - pos_dy * tex_dx.t);
                    vec3 b = normalize(-pos_dx * tex_dy.s + pos_dy * tex_dx.s);
                    mat3 tbn = mat3(t, b, normalize(vNormal));

                    vec3 n = texture(tNormal, vUv * uNormalUVScale).rgb * 2.0 - 1.0;
                    n.xy *= uNormalScale;
                    vec3 normal = normalize(tbn * n);

                    // Get world normal from view normal (normalMatrix * normal)
                    return normalize((vec4(normal, 0.0) * viewMatrix).xyz);
                }

                vec3 specularReflection(vec3 specularEnvR0, vec3 specularEnvR90, float VdH) {
                    return specularEnvR0 + (specularEnvR90 - specularEnvR0) * pow(clamp(1.0 - VdH, 0.0, 1.0), 5.0);
                }

                float geometricOcclusion(float NdL, float NdV, float roughness) {
                    float r = roughness;

                    float attenuationL = 2.0 * NdL / (NdL + sqrt(r * r + (1.0 - r * r) * (NdL * NdL)));
                    float attenuationV = 2.0 * NdV / (NdV + sqrt(r * r + (1.0 - r * r) * (NdV * NdV)));
                    return attenuationL * attenuationV;
                }

                float microfacetDistribution(float roughness, float NdH) {
                    float roughnessSq = roughness * roughness;
                    float f = (NdH * roughnessSq - NdH) * NdH + 1.0;
                    return roughnessSq / (PI * f * f);
                }

                vec2 cartesianToPolar(vec3 n) {
                    vec2 uv;
                    uv.x = atan(n.z, n.x) * RECIPROCAL_PI2 + 0.5;
                    uv.y = asin(n.y) * RECIPROCAL_PI + 0.5;
                    return uv;
                }

                void getIBLContribution(inout vec3 diffuse, inout vec3 specular, float NdV, float roughness, vec3 n, vec3 reflection, vec3 diffuseColor, vec3 specularColor) {
                    vec3 brdf = SRGBtoLinear(texture(tLUT, vec2(NdV, roughness))).rgb;

                    vec3 diffuseLight = RGBMToLinear(texture(tEnvDiffuse, cartesianToPolar(n))).rgb;

                    // Sample 2 levels and mix between to get smoother degradation
                    float blend = roughness * ENV_LODS;
                    float level0 = floor(blend);
                    float level1 = min(ENV_LODS, level0 + 1.0);
                    blend -= level0;

                    // Sample the specular env map atlas depending on the roughness value
                    vec2 uvSpec = cartesianToPolar(reflection);
                    uvSpec.y /= 2.0;

                    vec2 uv0 = uvSpec;
                    vec2 uv1 = uvSpec;

                    uv0 /= pow(2.0, level0);
                    uv0.y += 1.0 - exp(-LN2 * level0);

                    uv1 /= pow(2.0, level1);
                    uv1.y += 1.0 - exp(-LN2 * level1);

                    vec3 specular0 = RGBMToLinear(texture(tEnvSpecular, uv0)).rgb;
                    vec3 specular1 = RGBMToLinear(texture(tEnvSpecular, uv1)).rgb;
                    vec3 specularLight = mix(specular0, specular1, blend);

                    diffuse = diffuseLight * diffuseColor;

                    // Bit of extra reflection for smooth materials
                    float reflectivity = pow((1.0 - roughness), 2.0) * 0.05;
                    specular = specularLight * (specularColor * brdf.x + brdf.y + reflectivity);
                    specular *= uEnvSpecular;
                }

                // WebGL2版本的PBR主函数
                // 逻辑与WebGL1版本完全相同，主要差异是使用texture()替代texture2D()
                void main() {
                    // 使用WebGL2的texture()函数进行纹理采样
                    vec3 baseColor = SRGBtoLinear(texture(tBaseColor, vUv)).rgb * uBaseColor;

                    // RMO贴图采样：R=粗糙度，G=金属度，B=环境遮蔽
                    vec4 rmaSample = texture(tRMO, vUv);
                    float roughness = clamp(rmaSample.r * uRoughness, 0.04, 1.0);
                    float metallic = clamp(rmaSample.g * uMetallic, 0.04, 1.0);

                    vec3 f0 = vec3(0.04);
                    vec3 diffuseColor = baseColor * (vec3(1.0) - f0) * (1.0 - metallic);
                    vec3 specularColor = mix(f0, baseColor, metallic);

                    vec3 specularEnvR0 = specularColor;
                    vec3 specularEnvR90 = vec3(clamp(max(max(specularColor.r, specularColor.g), specularColor.b) * 25.0, 0.0, 1.0));

                     vec3 N = getNormal();
                    vec3 V = normalize(cameraPosition - vMPos);
                    vec3 L = normalize(uLightDirection);
                    vec3 H = normalize(L + V);
                    vec3 reflection = normalize(reflect(-V, N));

                    float NdL = clamp(dot(N, L), 0.001, 1.0);
                    float NdV = clamp(abs(dot(N, V)), 0.001, 1.0);
                    float NdH = clamp(dot(N, H), 0.0, 1.0);
                    float LdH = clamp(dot(L, H), 0.0, 1.0);
                    float VdH = clamp(dot(V, H), 0.0, 1.0);

                    vec3 F = specularReflection(specularEnvR0, specularEnvR90, VdH);
                    float G = geometricOcclusion(NdL, NdV, roughness);
                    float D = microfacetDistribution(roughness, NdH);

                    vec3 diffuseContrib = (1.0 - F) * (diffuseColor / PI);
                    vec3 specContrib = F * G * D / (4.0 * NdL * NdV);

                    // Shading based off lights
                    vec3 color = NdL * uLightColor * (diffuseContrib + specContrib);

                    // Get base alpha
                    float alpha = 1.0;
                    alpha *= texture(tOpacity, vUv).g;

                    // Add lights spec to alpha for reflections on transparent surfaces (glass)
                    alpha = max(alpha, max(max(specContrib.r, specContrib.g), specContrib.b));

                    // Calculate IBL lighting
                    vec3 diffuseIBL;
                    vec3 specularIBL;
                    getIBLContribution(diffuseIBL, specularIBL, NdV, roughness, N, reflection, diffuseColor, specularColor);

                    // Add IBL on top of color
                    color += diffuseIBL + specularIBL;

                    // Add IBL spec to alpha for reflections on transparent surfaces (glass)
                    alpha = max(alpha, max(max(specularIBL.r, specularIBL.g), specularIBL.b));

                    // Multiply occlusion
                    color = mix(color, color * rmaSample.b, uOcclusion);

                    // Add emissive on top
                    vec3 emissive = SRGBtoLinear(texture(tEmissive, vUv)).rgb * uEmissive;
                    color += emissive;

                    // Convert to sRGB to display
                    FragColor.rgb = linearToSRGB(color);

                    // Apply uAlpha uniform at the end to overwrite any specular additions on transparent surfaces
                    FragColor.a = alpha * uAlpha;
                }
            `;

            // 阴影渲染的顶点着色器
            // 用于渲染车辆底部的阴影效果，简化的顶点处理
            const shadowVertex = /* glsl */ `
                precision highp float;

                // 输入：UV坐标和顶点位置
                attribute vec2 uv;
                attribute vec3 position;

                // 标准的变换矩阵
                uniform mat4 modelViewMatrix;
                uniform mat4 projectionMatrix;

                // 输出：传递UV坐标用于阴影贴图采样
                varying vec2 vUv;

                void main() {
                    // 直接传递UV坐标到片段着色器
                    vUv = uv;

                    // 标准的顶点变换到裁剪空间
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // 阴影渲染的片段着色器
            // 创建半透明的阴影效果，使用贴图控制阴影的形状和强度
            const shadowFragment = /* glsl */ `
                precision highp float;

                // 阴影贴图，通常包含阴影的形状和强度信息
                uniform sampler2D tMap;

                // 从顶点着色器传入的UV坐标
                varying vec2 vUv;

                void main() {
                    // 从阴影贴图的绿色通道采样阴影强度
                    // 绿色通道通常用于存储透明度或遮罩信息
                    float shadow = texture2D(tMap, vUv).g;

                    // 输出纯黑色的阴影（RGB = 0）
                    gl_FragColor.rgb = vec3(0.0);

                    // 使用采样的值作为透明度，创建半透明阴影效果
                    gl_FragColor.a = shadow;
                }
            `;

            // 推荐使用此在线工具创建所需的IBL环境贴图
            // https://oframe.github.io/ibl-converter/

            {
                // 1. 初始化渲染器和WebGL上下文
                // dpr: 2 设置设备像素比为2，提供更清晰的渲染效果
                const renderer = new Renderer({ dpr: 2 });
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);

                // 设置深灰色背景，营造专业的展示环境
                gl.clearColor(0.1, 0.1, 0.1, 1);

                // 2. 创建透视相机
                // fov: 35度视野角，适合展示汽车模型的透视效果
                const camera = new Camera(gl, { fov: 35 });
                camera.position.set(2, 0.5, 3); // 相机位置：右上前方观察角度

                // 3. 设置轨道控制器，允许用户交互式查看模型
                const controls = new Orbit(camera);

                // 4. 窗口大小调整处理函数
                function resize() {
                    // 更新渲染器尺寸以匹配窗口
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    // 更新相机的宽高比以避免变形
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize(); // 初始化时调用一次

                // 5. 创建场景根节点
                const scene = new Transform();
                scene.position.y = -0.4; // 将整个场景稍微下移，使汽车底部接近地面

                // 6. 纹理缓存系统
                // 避免重复加载相同的纹理，提高性能和内存使用效率
                const textureCache = {};
                function getTexture(src, generateMipmaps = true) {
                    // 如果纹理已经加载过，直接返回缓存的纹理
                    if (textureCache[src]) return textureCache[src];

                    // 创建新的纹理对象，可选择是否生成mipmap
                    const texture = new Texture(gl, { generateMipmaps });
                    const image = new Image();

                    // 先将纹理存入缓存，避免重复创建
                    textureCache[src] = texture;

                    // 图片加载完成后，将其设置为纹理的图像数据
                    image.onload = () => {
                        texture.image = image;
                    };
                    image.src = src;
                    return texture;
                }

                // 7. 启动模型加载流程
                // 并行加载汽车的外观、内饰和阴影组件
                loadExterior(); // 加载汽车外观
                loadInterior(); // 加载汽车内饰
                loadShadow(); // 加载阴影平面

                // 8. 加载汽车外观模型
                async function loadExterior() {
                    // 异步加载外观几何数据（车身外壳）
                    const data = await (await fetch(`assets/pbr/car-ext.json`)).json();
                    // 异步加载内部几何数据（车身内侧，如轮拱等）
                    const dataInner = await (await fetch(`assets/pbr/car-ext-inner.json`)).json();

                    // 创建外观几何体
                    const geometry = new Geometry(gl, {
                        position: { size: 3, data: new Float32Array(data.position) }, // 顶点位置
                        uv: { size: 2, data: new Float32Array(data.uv) }, // UV纹理坐标
                        normal: { size: 3, data: new Float32Array(data.normal) }, // 顶点法线
                    });

                    // 创建内侧几何体（车身内部结构）
                    const geometryInner = new Geometry(gl, {
                        position: { size: 3, data: new Float32Array(dataInner.position) },
                        uv: { size: 2, data: new Float32Array(dataInner.uv) },
                        normal: { size: 3, data: new Float32Array(dataInner.normal) },
                    });

                    // 创建汽车外观的PBR着色器程序
                    // 这个复杂的着色器实现了完整的PBR渲染管线
                    const program = new Program(gl, {
                        // 根据WebGL版本选择对应的着色器（需要OES_standard_derivatives扩展）
                        vertex: renderer.isWebgl2 ? vertex300 : vertex100,
                        fragment: renderer.isWebgl2 ? fragment300 : fragment100,
                        uniforms: {
                            // 基础颜色/反照率贴图：决定材质的漫反射和镜面反射颜色
                            tBaseColor: { value: getTexture('assets/pbr/car-ext-color.jpg') },

                            // 基础颜色倍增器：用于调整整体颜色色调
                            uBaseColor: { value: new Color(1, 1, 1) },

                            // RMO打包贴图：R通道=粗糙度，G通道=金属度，B通道=环境遮蔽
                            tRMO: { value: getTexture('assets/pbr/car-ext-rmo.jpg') },

                            // 材质属性倍增器：用于微调PBR参数
                            uRoughness: { value: 1 }, // 粗糙度倍增器（影响镜面反射的锐利度）
                            uMetallic: { value: 1 }, // 金属度倍增器（决定材质的导电性）
                            uOcclusion: { value: 1 }, // 环境遮蔽强度（影响阴影深度）

                            // 法线贴图：提供表面细节和凹凸效果
                            tNormal: { value: getTexture('assets/pbr/car-ext-normal.jpg') },
                            uNormalScale: { value: 0.5 }, // 法线强度（0.5表示较温和的凹凸效果）
                            uNormalUVScale: { value: 1 }, // 法线贴图UV缩放

                            // 自发光贴图：模拟车灯等发光部件
                            tEmissive: { value: getTexture('assets/pbr/car-ext-emissive.jpg') },
                            uEmissive: { value: 1 }, // 自发光强度

                            // Initial opacity is taken from the green channel of the map below.
                            // If a transparent area is smooth, the specular may increase the opacity.
                            // This is done to simulate specular reflections on transparent surfaces like glass.
                            tOpacity: { value: getTexture('assets/pbr/car-ext-opacity.jpg') },

                            // uAlpha is an overall alpha control. It is applied right at the end to hide the geometry.
                            // Specular reflections will not affect this value, unlike above.
                            uAlpha: { value: 1 },

                            // This Look Up Table is used to calculate the BRDF (Bidirectional reflectance distribution function)
                            // coefficients used in the shader more efficiently.
                            // It is based on the roughness and fresnel grazing angle.
                            tLUT: { value: getTexture('assets/pbr/lut.png', false) },

                            // The following two environment maps are the most important inputs.
                            // They can be generated using this online tool https://oframe.github.io/ibl-converter/
                            // They are equirectangular (a sphere mapped to a rectangle) maps used for lighting the model.
                            // Instead of just relying on lights, we use these textures as IBL (image-based lighting), which
                            // is like having thousands of lights in a scene.
                            // In order to get more realistic results, we use a HDR (high dynamic range) image as an input,
                            // so instead of values being limited between 0 and 1, they can go higher (up to 6 in this implementation).
                            // These images have been converted to an RGBM structure (where the rgb channels multiply with the
                            // alpha channel to recapture their original HDR value), as this allows us to store it in an 8 bit PNG.

                            // The first of the two maps is the diffuse irradiance. It's a small, blurry texture used to give
                            // ambient/diffuse lighting to the model.
                            tEnvDiffuse: { value: getTexture('assets/pbr/waterfall-diffuse-RGBM.png', false) },

                            // The second is the pre-filtered specular vertical atlas. It's basically 7 environment maps
                            // in one, with each step half the size of the previous and also a bit blurrier.
                            // It's used for specular reflections, with the different levels to be sampled depending on how
                            // rough the model is at that point.
                            // I've used an atlas instead of mipmaps or texture arrays for simplicity's sake.
                            tEnvSpecular: { value: getTexture('assets/pbr/waterfall-specular-RGBM.png', false) },

                            // This is a multiplier to the amount of specular. Especially useful if you don't have an HDR map.
                            uEnvSpecular: { value: 2 },

                            // One light is included, ideally to simulate the sun, and both specular and diffuse are calculated.
                            uLightDirection: { value: new Vec3(0, 1, 1) },

                            // Here I've pushed the white light beyond 1 to increase its effect.
                            uLightColor: { value: new Vec3(7) },
                        },
                        transparent: true,
                    });

                    const mesh = new Mesh(gl, { geometry, program });
                    mesh.setParent(scene);

                    const meshInner = new Mesh(gl, { geometry: geometryInner, program });
                    meshInner.setParent(scene);
                }

                async function loadInterior() {
                    const data = await (await fetch(`assets/pbr/car-int.json`)).json();

                    const geometry = new Geometry(gl, {
                        position: { size: 3, data: new Float32Array(data.position) },
                        uv: { size: 2, data: new Float32Array(data.uv) },
                        normal: { size: 3, data: new Float32Array(data.normal) },
                    });

                    const program = new Program(gl, {
                        // Get fallback shader for WebGL1 - needed for OES_standard_derivatives ext
                        vertex: renderer.isWebgl2 ? vertex300 : vertex100,
                        fragment: renderer.isWebgl2 ? fragment300 : fragment100,
                        uniforms: {
                            tBaseColor: { value: getTexture('assets/pbr/car-int-color.jpg') },
                            uBaseColor: { value: new Color(1, 1, 1) },

                            tRMO: { value: getTexture('assets/pbr/car-int-rmo.jpg') },
                            uRoughness: { value: 1 },
                            uMetallic: { value: 1 },
                            uOcclusion: { value: 1 },

                            tNormal: { value: getTexture('assets/pbr/car-int-normal.jpg') },
                            uNormalScale: { value: 0.5 },
                            uNormalUVScale: { value: 1 },

                            tEmissive: { value: getTexture('assets/pbr/black.jpg') },
                            uEmissive: { value: 1 },

                            tOpacity: { value: getTexture('assets/pbr/white.jpg') },
                            uAlpha: { value: 1 },

                            tLUT: { value: getTexture('assets/pbr/lut.png', false) },

                            tEnvDiffuse: { value: getTexture('assets/pbr/waterfall-diffuse-RGBM.png', false) },
                            tEnvSpecular: { value: getTexture('assets/pbr/waterfall-specular-RGBM.png', false) },
                            uEnvSpecular: { value: 1.0 },

                            uLightDirection: { value: new Vec3(1, 1, 1) },
                            uLightColor: { value: new Vec3(7) },
                        },
                    });

                    const mesh = new Mesh(gl, { geometry, program });
                    mesh.setParent(scene);
                }

                // 10. 加载阴影平面
                function loadShadow() {
                    // 创建地面阴影的平面几何体（2.3x2.3单位）
                    const geometry = new Plane(gl, { width: 2.3, height: 2.3 });

                    // 创建阴影着色器程序
                    const program = new Program(gl, {
                        vertex: shadowVertex, // 简单的阴影顶点着色器
                        fragment: shadowFragment, // 阴影片段着色器
                        uniforms: {
                            // 阴影贴图：定义阴影的形状和透明度
                            tMap: { value: getTexture('assets/pbr/car-shadow.jpg') },
                        },
                        transparent: true, // 启用透明度混合
                        cullFace: false, // 禁用背面剔除（双面渲染）
                    });

                    // 创建阴影网格并设置变换
                    const mesh = new Mesh(gl, { geometry, program });
                    mesh.rotation.x = -Math.PI / 2; // 旋转90度使平面水平放置
                    mesh.setParent(scene); // 添加到场景中
                }

                // 11. 启动渲染循环
                requestAnimationFrame(update);

                // 主渲染循环函数
                function update() {
                    // 请求下一帧动画，创建60FPS的渲染循环
                    requestAnimationFrame(update);

                    // 缓慢旋转整个场景，展示汽车的各个角度
                    scene.rotation.y += 0.005; // 每帧旋转0.005弧度

                    // 更新轨道控制器（处理用户交互）
                    controls.update();

                    // 执行渲染：将场景通过相机渲染到画布
                    renderer.render({ scene, camera });
                }
            }
        </script>
    </body>
</html>
