// ========================================
// 阴影映射系统核心模块
// ========================================
// 功能：实现基于深度纹理的实时阴影映射
// 支持：阴影投射、阴影接收、自定义深度着色器

import { Camera } from '../core/Camera.js';
import { Program } from '../core/Program.js';
import { RenderTarget } from '../core/RenderTarget.js';

/**
 * Shadow 类 - OGL 阴影映射系统
 *
 * 实现原理：
 * 1. 从光源视角渲染场景深度到纹理 (Shadow Map)
 * 2. 从主相机视角渲染时，比较像素深度与阴影贴图
 * 3. 根据深度差异判断像素是否在阴影中
 */
export class Shadow {
    /**
     * 构造函数 - 初始化阴影映射系统
     * @param {WebGLRenderingContext} gl - WebGL 上下文
     * @param {Object} options - 配置选项
     * @param {Camera} options.light - 光源相机 (默认创建新相机)
     * @param {number} options.width - 阴影贴图宽度 (默认1024)
     * @param {number} options.height - 阴影贴图高度 (默认等于宽度)
     */
    constructor(gl, { light = new Camera(gl), width = 1024, height = width }) {
        this.gl = gl;

        // 光源相机：定义阴影投射的视角和范围
        // 可以是正交相机 (平行光) 或透视相机 (点光源)
        this.light = light;

        // 阴影贴图渲染目标：存储从光源视角看到的深度信息
        // 更高的分辨率 = 更精细的阴影细节，但性能开销更大
        this.target = new RenderTarget(gl, { width, height });

        // 阴影纹理 uniform：将阴影贴图传递给着色器
        this.targetUniform = { value: this.target.texture };

        // 默认深度渲染程序：用于生成阴影贴图
        // 只渲染深度信息，不需要颜色和光照计算
        this.depthProgram = new Program(gl, {
            vertex: defaultVertex, // 简单的顶点变换
            fragment: defaultFragment, // 深度值打包到RGBA
            cullFace: false, // 禁用面剔除，确保完整深度信息
        });

        // 投射阴影的网格列表
        // 只有在此列表中的网格才会在深度渲染阶段被绘制
        this.castMeshes = [];
    }

    /**
     * 添加网格到阴影系统
     * @param {Object} options - 配置选项
     * @param {Mesh} options.mesh - 要添加的网格对象
     * @param {boolean} options.receive - 是否接收阴影 (默认true)
     * @param {boolean} options.cast - 是否投射阴影 (默认true)
     * @param {string} options.vertex - 自定义深度顶点着色器 (可选)
     * @param {string} options.fragment - 自定义深度片段着色器 (可选)
     * @param {string} options.uniformProjection - 光源投影矩阵uniform名称
     * @param {string} options.uniformView - 光源视图矩阵uniform名称
     * @param {string} options.uniformTexture - 阴影纹理uniform名称
     */
    add({
        mesh,
        receive = true,
        cast = true,
        vertex = defaultVertex,
        fragment = defaultFragment,
        uniformProjection = 'shadowProjectionMatrix',
        uniformView = 'shadowViewMatrix',
        uniformTexture = 'tShadow',
    }) {
        // ========================================
        // 阴影接收配置
        // ========================================
        // 为网格的着色器程序注入阴影相关的uniform变量
        if (receive && !mesh.program.uniforms[uniformProjection]) {
            // 光源投影矩阵：将世界坐标转换到光源的投影空间
            mesh.program.uniforms[uniformProjection] = { value: this.light.projectionMatrix };

            // 光源视图矩阵：将世界坐标转换到光源的视图空间
            mesh.program.uniforms[uniformView] = { value: this.light.viewMatrix };

            // 阴影纹理：包含从光源视角渲染的深度信息
            mesh.program.uniforms[uniformTexture] = this.targetUniform;
        }

        // 如果不投射阴影，直接返回
        if (!cast) return;

        // 将网格添加到投射阴影列表
        this.castMeshes.push(mesh);

        // ========================================
        // 深度渲染程序配置
        // ========================================

        // 保存原始着色器程序 (用于正常渲染)
        mesh.colorProgram = mesh.program;

        // 检查是否已经配置了深度程序，避免重复设置
        if (mesh.depthProgram) return;

        // 使用默认深度着色器 (性能优化：共享程序实例)
        if (vertex === defaultVertex && fragment === defaultFragment) {
            mesh.depthProgram = this.depthProgram;
            return;
        }

        // 创建自定义深度渲染程序
        // 用于特殊需求，如透明物体、顶点动画等
        mesh.depthProgram = new Program(this.gl, {
            vertex, // 自定义顶点着色器
            fragment, // 自定义片段着色器
            cullFace: false, // 禁用面剔除，确保完整深度信息
        });
    }

    /**
     * 设置阴影贴图尺寸
     * @param {Object} options - 尺寸配置
     * @param {number} options.width - 宽度 (默认1024)
     * @param {number} options.height - 高度 (默认等于宽度)
     */
    setSize({ width = 1024, height = width }) {
        // 重新创建渲染目标，更新阴影贴图分辨率
        this.target = new RenderTarget(this.gl, { width, height });

        // 更新纹理引用，确保着色器使用新的阴影贴图
        this.targetUniform.value = this.target.texture;
    }

    /**
     * 渲染阴影贴图
     * @param {Object} options - 渲染配置
     * @param {Transform} options.scene - 场景根节点
     */
    render({ scene }) {
        // ========================================
        // 第一阶段：准备深度渲染
        // ========================================
        // 遍历场景，配置每个节点的渲染状态
        scene.traverse((node) => {
            // 跳过非渲染节点
            if (!node.draw) return;

            // 检查节点是否在投射阴影列表中
            // !!~array.indexOf(item) 是检查数组包含的惯用法
            // ~ 是按位取反，indexOf返回-1时，~(-1) = 0，!!0 = false
            // indexOf返回>=0时，~n < 0，!!负数 = true
            if (!!~this.castMeshes.indexOf(node)) {
                // 投射阴影的网格：切换到深度渲染程序
                node.program = node.depthProgram;
            } else {
                // 不投射阴影的网格：临时隐藏
                node.isForceVisibility = node.visible; // 保存原始可见性
                node.visible = false; // 在深度渲染中隐藏
            }
        });

        // ========================================
        // 第二阶段：执行深度渲染
        // ========================================
        // 从光源视角渲染场景深度到阴影贴图
        // 只有投射阴影的网格会被渲染 (其他已被隐藏)
        this.gl.renderer.render({
            scene, // 场景图
            camera: this.light, // 使用光源作为相机
            target: this.target, // 渲染到阴影贴图而非屏幕
        });

        // ========================================
        // 第三阶段：恢复正常渲染状态
        // ========================================
        // 将所有节点恢复到正常渲染状态
        scene.traverse((node) => {
            if (!node.draw) return;

            if (!!~this.castMeshes.indexOf(node)) {
                // 投射阴影的网格：恢复到颜色渲染程序
                node.program = node.colorProgram;
            } else {
                // 不投射阴影的网格：恢复原始可见性
                node.visible = node.isForceVisibility;
            }
        });
    }
}

// ========================================
// 默认深度渲染着色器
// ========================================

/**
 * 默认深度顶点着色器
 * 功能：执行基本的顶点变换，将顶点从模型空间转换到裁剪空间
 * 用途：生成阴影贴图时的顶点处理
 */
const defaultVertex = /* glsl */ `
    // 顶点属性
    attribute vec3 position;    // 顶点位置 (模型空间)
    attribute vec2 uv;          // 纹理坐标 (未使用，但保持兼容性)

    // 变换矩阵 (由 OGL 自动注入)
    uniform mat4 modelViewMatrix;   // 模型视图矩阵 (模型空间 -> 视图空间)
    uniform mat4 projectionMatrix;  // 投影矩阵 (视图空间 -> 裁剪空间)

    void main() {
        // 标准顶点变换：模型空间 -> 世界空间 -> 视图空间 -> 裁剪空间
        // 在深度渲染中，我们只关心顶点的最终位置，不需要其他计算
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
`;

/**
 * 默认深度片段着色器
 * 功能：将深度值打包到RGBA颜色中存储
 * 原因：某些WebGL实现不支持浮点深度纹理，需要用RGBA编码深度
 */
const defaultFragment = /* glsl */ `
    precision highp float;

    /**
     * RGBA深度打包函数
     * 将32位浮点深度值编码为RGBA四个8位通道
     * @param {float} v - 深度值 [0.0, 1.0]
     * @return {vec4} - RGBA编码的深度值
     */
    vec4 packRGBA (float v) {
        // 将深度值分解为4个精度级别
        // R: 1.0        (整数部分)
        // G: 255.0      (1/255 精度)
        // B: 65025.0    (1/65025 精度)
        // A: 16581375.0 (1/16581375 精度)
        vec4 pack = fract(vec4(1.0, 255.0, 65025.0, 16581375.0) * v);

        // 减去高位溢出，确保每个通道独立
        // pack.yzww 获取 [G, B, A, A]
        // vec2(1.0/255.0, 0.0).xxxy 创建 [1/255, 1/255, 1/255, 0]
        // 这样可以防止精度溢出到下一个通道
        pack -= pack.yzww * vec2(1.0 / 255.0, 0.0).xxxy;

        return pack;
    }

    void main() {
        // 将当前片段的深度值 (gl_FragCoord.z) 打包到颜色输出
        // gl_FragCoord.z 是标准化的深度值 [0.0, 1.0]
        // 0.0 = 近裁剪面，1.0 = 远裁剪面
        gl_FragColor = packRGBA(gl_FragCoord.z);
    }
`;
