<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Post Bloom Effect</title>
        <link href="assets/main.css" rel="stylesheet" />
        <style>
            .controls {
                position: absolute;
                top: 50px;
                left: 20px;
                background: rgba(0, 0, 0, 0.8);
                padding: 20px;
                border-radius: 8px;
                color: white;
                font-family: monospace;
                font-size: 12px;
                z-index: 100;
                min-width: 250px;
            }
            .control-group {
                margin-bottom: 15px;
            }
            .control-group label {
                display: block;
                margin-bottom: 5px;
                color: #ccc;
            }
            .control-group input[type='range'] {
                width: 100%;
                margin-bottom: 5px;
            }
            .control-group input[type='checkbox'] {
                margin-right: 8px;
            }
            .value-display {
                color: #4caf50;
                font-weight: bold;
            }
            .section-title {
                color: #2196f3;
                font-weight: bold;
                margin-bottom: 10px;
                border-bottom: 1px solid #333;
                padding-bottom: 5px;
            }
        </style>
    </head>
    <body>
        <div class="Info" style="color: #fff">Post Bloom Effect - 后期处理光晕效果</div>

        <div class="controls">
            <div class="section-title">Bloom Parameters / 光晕参数</div>

            <div class="control-group">
                <label>Bloom Strength / 光晕强度: <span class="value-display" id="bloomStrengthValue">1.0</span></label>
                <input type="range" id="bloomStrength" min="0" max="3" step="0.1" value="1.0" />
            </div>

            <div class="control-group">
                <label>Brightness Threshold / 亮度阈值: <span class="value-display" id="thresholdValue">0.8</span></label>
                <input type="range" id="threshold" min="0" max="2" step="0.05" value="0.8" />
            </div>

            <div class="control-group">
                <label>Blur Intensity / 模糊强度: <span class="value-display" id="blurIntensityValue">2.0</span></label>
                <input type="range" id="blurIntensity" min="0.5" max="5" step="0.1" value="2.0" />
            </div>

            <div class="control-group">
                <label>Blur Iterations / 模糊迭代次数: <span class="value-display" id="blurIterationsValue">5</span></label>
                <input type="range" id="blurIterations" min="1" max="10" step="1" value="5" />
            </div>

            <div class="control-group">
                <label>
                    <input type="checkbox" id="highQualityBlur" />
                    High Quality Blur / 高质量模糊 (blur9)
                </label>
            </div>

            <div class="control-group">
                <label>
                    <input type="checkbox" id="enableBloom" checked />
                    Enable Bloom / 启用光晕效果
                </label>
            </div>
        </div>
        <script type="module">
            import { Renderer, Camera, Program, Mesh, Vec2, Post, Box, Transform } from '../src/index.js';

            /**
             * 亮度提取着色器 - Bright Pass Fragment Shader
             * 功能：从原始场景中提取亮度超过阈值的像素，这些像素将产生光晕效果
             * 原理：使用step函数创建硬边界，只保留亮度足够高的像素
             */
            const brightPassFragment = /* glsl */ `
                precision highp float;
                uniform sampler2D tMap;      // 输入纹理（原始场景）
                uniform float uThreshold;   // 亮度阈值，控制哪些像素会发光

                varying vec2 vUv;           // 纹理坐标

                void main() {
                    vec4 tex = texture2D(tMap, vUv);
                    // 计算像素的亮度值：length(tex.rgb) / sqrt(3)
                    // 1.73205 = sqrt(3)，用于将RGB向量长度标准化到[0,1]范围
                    // step(threshold, brightness) 创建硬边界：亮度>=阈值时返回1，否则返回0
                    vec4 bright = tex * step(uThreshold, length(tex.rgb) / 1.73205);
                    gl_FragColor = bright;
                }
            `;

            /**
             * 高斯模糊着色器 - Gaussian Blur Fragment Shader
             * 功能：对亮度提取后的图像进行模糊处理，创建光晕的扩散效果
             * 原理：使用分离式高斯模糊（先水平后垂直），提高性能
             */
            const blurFragment = /* glsl */ `
                precision highp float;

                /**
                 * 5点高斯模糊 - 快速版本
                 * 参考：https://github.com/Jam3/glsl-fast-gaussian-blur/blob/master/5.glsl
                 * 性能：高，质量：中等
                 * 原理：使用预计算的权重和偏移量，减少纹理采样次数
                 */
                vec4 blur5(sampler2D image, vec2 uv, vec2 resolution, vec2 direction) {
                    vec4 color = vec4(0.0);
                    // 偏移量：1.3333... 是优化后的采样位置，可以用更少的采样获得更好的效果
                    vec2 off1 = vec2(1.3333333333333333) * direction;
                    // 中心像素权重：0.294...
                    color += texture2D(image, uv) * 0.29411764705882354;
                    // 两侧像素权重：0.352...（对称采样）
                    color += texture2D(image, uv + (off1 / resolution)) * 0.35294117647058826;
                    color += texture2D(image, uv - (off1 / resolution)) * 0.35294117647058826;
                    return color;
                }

                /**
                 * 9点高斯模糊 - 高质量版本
                 * 参考：https://github.com/Jam3/glsl-fast-gaussian-blur/blob/master/9.glsl
                 * 性能：中等，质量：高
                 * 原理：更多的采样点和更精确的权重分布，产生更平滑的模糊效果
                 */
                vec4 blur9(sampler2D image, vec2 uv, vec2 resolution, vec2 direction) {
                    vec4 color = vec4(0.0);
                    // 两个不同的偏移量，用于采样更远的像素
                    vec2 off1 = vec2(1.3846153846) * direction;      // 近距离偏移
                    vec2 off2 = vec2(3.2307692308) * direction;      // 远距离偏移
                    // 中心像素权重
                    color += texture2D(image, uv) * 0.2270270270;
                    // 近距离像素权重（对称采样）
                    color += texture2D(image, uv + (off1 / resolution)) * 0.3162162162;
                    color += texture2D(image, uv - (off1 / resolution)) * 0.3162162162;
                    // 远距离像素权重（对称采样）
                    color += texture2D(image, uv + (off2 / resolution)) * 0.0702702703;
                    color += texture2D(image, uv - (off2 / resolution)) * 0.0702702703;
                    return color;
                }

                uniform sampler2D tMap;      // 输入纹理（亮度提取后的图像）
                uniform vec2 uDirection;     // 模糊方向：(x,0)水平模糊，(0,y)垂直模糊
                uniform vec2 uResolution;    // 屏幕分辨率，用于计算正确的采样偏移
                uniform bool uHighQuality;  // 质量开关：true使用blur9，false使用blur5

                varying vec2 vUv;           // 纹理坐标

                void main() {
                    // 根据质量设置选择不同的模糊算法
                    if (uHighQuality) {
                        gl_FragColor = blur9(tMap, vUv, uResolution, uDirection);
                    } else {
                        gl_FragColor = blur5(tMap, vUv, uResolution, uDirection);
                    }
                }
            `;

            /**
             * 合成着色器 - Composite Fragment Shader
             * 功能：将原始场景和模糊后的光晕图像合成，产生最终的bloom效果
             * 原理：简单的加法混合，可以通过强度参数控制光晕的显著程度
             */
            const compositeFragment = /* glsl */ `
                precision highp float;

                uniform sampler2D tMap;          // 原始场景纹理
                uniform sampler2D tBloom;        // 模糊后的光晕纹理
                uniform vec2 uResolution;        // 屏幕分辨率（备用）
                uniform float uBloomStrength;   // 光晕强度，控制光晕效果的显著程度

                varying vec2 vUv;               // 纹理坐标

                void main() {
                    // 简单的加法混合：原始场景 + 光晕效果 * 强度
                    // 这种混合方式会让亮区域变得更亮，产生"发光"的视觉效果
                    gl_FragColor = texture2D(tMap, vUv) + texture2D(tBloom, vUv) * uBloomStrength;
                }
            `;

            {
                /**
                 * ========== 渲染器和相机设置 ==========
                 */
                // 创建WebGL渲染器，dpr: 1 表示使用标准像素密度
                const renderer = new Renderer({ dpr: 1 });
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);
                // 设置清除颜色为深蓝色
                gl.clearColor(0.0, 0.0, 0.1, 1);

                // 创建透视相机
                const camera = new Camera(gl, { fov: 35 });
                camera.position.set(0, 1, 5); // 相机位置
                camera.lookAt([0, 0, 0]); // 看向原点

                /**
                 * ========== 后处理管道设置 ==========
                 * Bloom效果需要两个后处理管道：
                 * 1. postComposite: 全分辨率，用于最终合成
                 * 2. postBloom: 半分辨率，用于生成光晕效果（提高性能）
                 */
                // 创建合成后处理管道（全分辨率）
                const postComposite = new Post(gl);
                // 创建光晕后处理管道（半分辨率，只渲染到纹理不直接显示）
                // `targetOnly: true` 防止直接渲染到画布
                const postBloom = new Post(gl, { dpr: 0.5, targetOnly: true });

                /**
                 * ========== Uniform变量设置 ==========
                 * 这些变量会传递给着色器，用于控制渲染参数
                 */
                // 全分辨率尺寸（用于合成阶段）
                const resolution = { value: new Vec2() };
                // 光晕分辨率尺寸（用于模糊阶段）
                const bloomResolution = { value: new Vec2() };

                /**
                 * ========== 场景和渲染对象 ==========
                 */
                const scene = new Transform(); // 场景根节点

                let mesh; // 3D网格对象（立方体）
                // 存储各个渲染pass的引用，用于动态控制
                let compositePass; // 合成pass
                let brightPass; // 亮度提取pass
                let horizontalPass; // 水平模糊pass
                let verticalPass; // 垂直模糊pass

                /**
                 * ========== 可控制的参数 ==========
                 * 这些参数通过UI控件进行调整，实时影响bloom效果
                 */
                const params = {
                    bloomStrength: 1.0, // 光晕强度：控制最终光晕的显著程度
                    threshold: 0.8, // 亮度阈值：控制哪些像素会发光
                    blurIntensity: 2.0, // 模糊强度：控制光晕的扩散范围
                    blurIterations: 5, // 模糊迭代次数：影响光晕的平滑度
                    highQualityBlur: false, // 高质量模糊：blur9 vs blur5
                    enableBloom: true, // 启用光晕：完全开关bloom效果
                };

                /**
                 * ========== 初始化流程 ==========
                 */
                {
                    initScene(); // 创建3D场景
                    initPasses(); // 设置后处理管道
                    initControls(); // 绑定UI控件事件
                }

                /**
                 * 初始化3D场景
                 * 创建一个彩色立方体作为演示对象
                 */
                function initScene() {
                    // 创建立方体几何体
                    const geometry = new Box(gl);

                    // 创建着色器程序
                    const program = new Program(gl, {
                        // 顶点着色器：处理顶点位置变换
                        vertex: /* glsl */ `
                            attribute vec3 position;        // 顶点位置
                            attribute vec2 uv;              // 纹理坐标
                            uniform mat4 modelViewMatrix;   // 模型视图矩阵
                            uniform mat4 projectionMatrix;  // 投影矩阵

                            varying vec2 vUv;               // 传递给片段着色器的纹理坐标

                            void main() {
                                vUv = uv;
                                // 标准的MVP变换：投影矩阵 * 模型视图矩阵 * 顶点位置
                                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.);
                            }
                        `,
                        // 片段着色器：根据UV坐标生成彩色效果
                        fragment: /* glsl */ `
                            precision highp float;

                            varying vec2 vUv;               // 从顶点着色器接收的纹理坐标

                            void main() {
                                // 使用UV坐标作为颜色：红色=U坐标，绿色=V坐标，蓝色=1.0，透明度=1.0
                                // 这会产生从左下角(黑色)到右上角(黄色)的渐变效果
                                gl_FragColor = vec4(vUv, 1.0, 1.0);
                            }
                        `,
                    });

                    // 创建网格对象并添加到场景
                    mesh = new Mesh(gl, { geometry, program });
                    mesh.setParent(scene);
                }

                /**
                 * 初始化后处理管道
                 * 设置bloom效果的三个主要阶段：亮度提取 -> 模糊处理 -> 最终合成
                 */
                function initPasses() {
                    /**
                     * 第一阶段：亮度提取 (Bright Pass)
                     * 从原始场景中提取亮度超过阈值的像素
                     * 这些像素将成为光晕效果的源头
                     */
                    brightPass = postBloom.addPass({
                        fragment: brightPassFragment,
                        uniforms: {
                            uThreshold: { value: params.threshold }, // 亮度阈值
                        },
                    });

                    /**
                     * 第二阶段：高斯模糊处理 (Gaussian Blur)
                     * 使用分离式模糊：先水平模糊，再垂直模糊
                     * 这种方法比直接2D模糊更高效（O(n) vs O(n²)）
                     */
                    // 水平模糊pass
                    horizontalPass = postBloom.addPass({
                        fragment: blurFragment,
                        uniforms: {
                            uResolution: bloomResolution, // 模糊分辨率
                            uDirection: { value: new Vec2(params.blurIntensity, 0) }, // 水平方向
                            uHighQuality: { value: params.highQualityBlur }, // 质量设置
                        },
                    });

                    // 垂直模糊pass
                    verticalPass = postBloom.addPass({
                        fragment: blurFragment,
                        uniforms: {
                            uResolution: bloomResolution, // 模糊分辨率
                            uDirection: { value: new Vec2(0, params.blurIntensity) }, // 垂直方向
                            uHighQuality: { value: params.highQualityBlur }, // 质量设置
                        },
                    });

                    // 根据迭代次数设置模糊pass的重复次数
                    updateBlurPasses();

                    /**
                     * 第三阶段：最终合成 (Composite)
                     * 将原始场景和模糊后的光晕图像合成
                     * 产生最终的bloom效果
                     */
                    compositePass = postComposite.addPass({
                        fragment: compositeFragment,
                        uniforms: {
                            uResolution: resolution, // 屏幕分辨率
                            tBloom: postBloom.uniform, // 光晕纹理（来自bloom管道）
                            uBloomStrength: { value: params.bloomStrength }, // 光晕强度
                        },
                    });
                }

                /**
                 * 更新模糊pass的数量
                 * 根据迭代次数参数动态调整模糊pass的重复次数
                 * 更多迭代 = 更平滑的光晕效果，但性能开销更大
                 */
                function updateBlurPasses() {
                    // 清除现有的模糊pass（保留第一个亮度提取pass）
                    postBloom.passes = postBloom.passes.slice(0, 1);

                    // 根据迭代参数重新添加高斯模糊pass
                    // 每次迭代包含一个水平模糊和一个垂直模糊
                    for (let i = 0; i < params.blurIterations; i++) {
                        postBloom.passes.push(horizontalPass, verticalPass);
                    }
                }

                /**
                 * 初始化UI控件事件处理
                 * 将HTML控件与着色器参数绑定，实现实时调整效果
                 */
                function initControls() {
                    /**
                     * 光晕强度控制
                     * 影响：最终合成阶段光晕纹理的混合强度
                     * 效果：值越大，光晕效果越明显
                     */
                    const bloomStrengthSlider = document.getElementById('bloomStrength');
                    const bloomStrengthValue = document.getElementById('bloomStrengthValue');
                    bloomStrengthSlider.addEventListener('input', (e) => {
                        params.bloomStrength = parseFloat(e.target.value);
                        bloomStrengthValue.textContent = params.bloomStrength.toFixed(1);
                        // 直接更新合成pass的uniform变量
                        compositePass.uniforms.uBloomStrength.value = params.bloomStrength;
                    });

                    /**
                     * 亮度阈值控制
                     * 影响：决定哪些像素会产生光晕效果
                     * 效果：值越低，越多的像素会发光；值越高，只有最亮的像素才会发光
                     */
                    const thresholdSlider = document.getElementById('threshold');
                    const thresholdValue = document.getElementById('thresholdValue');
                    thresholdSlider.addEventListener('input', (e) => {
                        params.threshold = parseFloat(e.target.value);
                        thresholdValue.textContent = params.threshold.toFixed(2);
                        // 更新亮度提取pass的阈值
                        brightPass.uniforms.uThreshold.value = params.threshold;
                    });

                    /**
                     * 模糊强度控制
                     * 影响：控制高斯模糊的扩散范围
                     * 效果：值越大，光晕扩散得越远，看起来越"柔和"
                     */
                    const blurIntensitySlider = document.getElementById('blurIntensity');
                    const blurIntensityValue = document.getElementById('blurIntensityValue');
                    blurIntensitySlider.addEventListener('input', (e) => {
                        params.blurIntensity = parseFloat(e.target.value);
                        blurIntensityValue.textContent = params.blurIntensity.toFixed(1);
                        // 更新水平和垂直模糊pass的方向向量
                        horizontalPass.uniforms.uDirection.value.set(params.blurIntensity, 0);
                        verticalPass.uniforms.uDirection.value.set(0, params.blurIntensity);
                    });

                    /**
                     * 模糊迭代次数控制
                     * 影响：控制模糊pass的重复次数
                     * 效果：更多迭代产生更平滑、更均匀的光晕，但性能开销更大
                     */
                    const blurIterationsSlider = document.getElementById('blurIterations');
                    const blurIterationsValue = document.getElementById('blurIterationsValue');
                    blurIterationsSlider.addEventListener('input', (e) => {
                        params.blurIterations = parseInt(e.target.value);
                        blurIterationsValue.textContent = params.blurIterations;
                        // 重新构建模糊pass链
                        updateBlurPasses();
                    });

                    /**
                     * 高质量模糊开关
                     * 影响：切换blur5（快速）和blur9（高质量）算法
                     * 效果：高质量模糊提供更好的视觉效果，但GPU开销更大
                     */
                    const highQualityBlurCheckbox = document.getElementById('highQualityBlur');
                    highQualityBlurCheckbox.addEventListener('change', (e) => {
                        params.highQualityBlur = e.target.checked;
                        // 更新两个模糊pass的质量设置
                        horizontalPass.uniforms.uHighQuality.value = params.highQualityBlur;
                        verticalPass.uniforms.uHighQuality.value = params.highQualityBlur;
                    });

                    /**
                     * 光晕效果总开关
                     * 影响：完全启用或禁用bloom效果
                     * 效果：关闭时直接渲染原始场景，开启时使用完整的bloom管道
                     */
                    const enableBloomCheckbox = document.getElementById('enableBloom');
                    enableBloomCheckbox.addEventListener('change', (e) => {
                        params.enableBloom = e.target.checked;
                    });
                }

                /**
                 * 窗口大小调整处理
                 * 当浏览器窗口大小改变时，需要更新渲染器、相机和后处理管道
                 */
                function resize() {
                    const { innerWidth: width, innerHeight: height } = window;

                    // 更新渲染器画布大小
                    renderer.setSize(width, height);
                    // 更新相机宽高比
                    camera.perspective({ aspect: width / height });

                    // 更新后处理管道的分辨率
                    postComposite.resize(); // 全分辨率管道
                    postBloom.resize(); // 半分辨率管道

                    // 更新着色器中的分辨率uniform变量
                    resolution.value.set(width, height);
                    bloomResolution.value.set(postBloom.resolutionWidth, postBloom.resolutionHeight);
                }

                // 监听窗口大小变化事件
                window.addEventListener('resize', resize, false);
                resize(); // 初始化时调用一次

                /**
                 * ========== 主渲染循环 ==========
                 * 这是bloom效果的核心渲染流程
                 */
                requestAnimationFrame(update);
                function update() {
                    requestAnimationFrame(update);

                    // 简单的立方体动画
                    mesh.rotation.y -= 0.005; // Y轴旋转
                    mesh.rotation.x -= 0.01; // X轴旋转

                    if (params.enableBloom) {
                        /**
                         * ========== Bloom渲染管道 ==========
                         * 多pass渲染流程：场景渲染 -> 亮度提取 -> 模糊处理 -> 最终合成
                         */

                        // 第一步：渲染原始场景到纹理
                        // 禁用合成pass，只渲染场景
                        compositePass.enabled = false;
                        // 设置为只渲染到纹理，不显示到画布
                        postComposite.targetOnly = true;
                        // 渲染场景到 postComposite.uniform.value 纹理
                        postComposite.render({ scene, camera });

                        // 第二步：处理bloom效果（亮度提取 + 模糊）
                        // 使用第一步的渲染结果作为输入，避免重复渲染场景
                        // 这会依次执行：brightPass -> horizontalPass -> verticalPass (重复N次)
                        postBloom.render({ texture: postComposite.uniform.value });

                        // 第三步：最终合成
                        // 重新启用合成pass
                        compositePass.enabled = true;
                        // 允许渲染到画布
                        postComposite.targetOnly = false;
                        // 合成原始场景和bloom效果，输出到画布
                        // 合成pass会接收：原始场景纹理 + bloom纹理
                        postComposite.render({ texture: postComposite.uniform.value });
                    } else {
                        /**
                         * ========== 直接渲染模式 ==========
                         * 当bloom效果被禁用时，直接渲染场景到画布
                         */
                        renderer.render({ scene, camera });
                    }
                }
            }
        </script>
    </body>
</html>
