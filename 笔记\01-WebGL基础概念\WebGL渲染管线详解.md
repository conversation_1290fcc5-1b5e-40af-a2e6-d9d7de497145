# WebGL 渲染管线详解

## 概述

WebGL 渲染管线是一个高度并行化的图形处理流水线，将 3D 几何数据转换为 2D 屏幕像素。整个过程分为 6 个主要阶段：

```
顶点数据 → 顶点着色器 → 图元装配 → 光栅化 → 片元着色器 → 输出合并
```

## 1. 顶点数据阶段 (Vertex Data)

### 1.1 数据来源

顶点数据是渲染管线的输入，包含描述 3D 模型的所有信息：

```javascript
// 典型的顶点数据结构
const vertexData = {
    // 位置坐标 (x, y, z)
    position: [
        -1.0,
        -1.0,
        0.0, // 顶点0
        1.0,
        -1.0,
        0.0, // 顶点1
        0.0,
        1.0,
        0.0, // 顶点2
    ],

    // 法线向量 (nx, ny, nz)
    normal: [
        0.0,
        0.0,
        1.0, // 顶点0法线
        0.0,
        0.0,
        1.0, // 顶点1法线
        0.0,
        0.0,
        1.0, // 顶点2法线
    ],

    // 纹理坐标 (u, v)
    uv: [
        0.0,
        0.0, // 顶点0 UV
        1.0,
        0.0, // 顶点1 UV
        0.5,
        1.0, // 顶点2 UV
    ],

    // 顶点颜色 (r, g, b, a)
    color: [
        1.0,
        0.0,
        0.0,
        1.0, // 红色
        0.0,
        1.0,
        0.0,
        1.0, // 绿色
        0.0,
        0.0,
        1.0,
        1.0, // 蓝色
    ],
};
```

### 1.2 数据存储

顶点数据存储在 GPU 的顶点缓冲对象(VBO)中：

```javascript
// 创建并绑定顶点缓冲区
const positionBuffer = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(positions), gl.STATIC_DRAW);

// 设置顶点属性指针
gl.vertexAttribPointer(
    0, // 属性索引
    3, // 每个顶点的分量数 (x, y, z)
    gl.FLOAT, // 数据类型
    false, // 是否归一化
    0, // 步长
    0 // 偏移量
);
gl.enableVertexAttribArray(0);
```

### 1.3 索引数据

定义顶点连接关系，减少重复顶点：

```javascript
// 索引数组定义三角形
const indices = [
    0,
    1,
    2, // 第一个三角形
    2,
    3,
    0, // 第二个三角形
];

const indexBuffer = gl.createBuffer();
gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, new Uint16Array(indices), gl.STATIC_DRAW);
```

## 2. 顶点着色器阶段 (Vertex Shader)

### 2.1 主要职责

顶点着色器对每个顶点独立执行，主要完成：

1. **坐标变换**：将顶点从模型空间转换到裁剪空间
2. **光照计算**：计算顶点级光照（如果需要）
3. **数据传递**：向片元着色器传递插值数据

### 2.2 坐标变换过程

```glsl
// 顶点着色器示例
attribute vec3 position;    // 输入：模型空间坐标
attribute vec3 normal;      // 输入：法线向量
attribute vec2 uv;          // 输入：纹理坐标

uniform mat4 modelMatrix;      // 模型矩阵
uniform mat4 viewMatrix;       // 视图矩阵
uniform mat4 projectionMatrix; // 投影矩阵
uniform mat3 normalMatrix;     // 法线矩阵

varying vec3 vWorldPos;     // 输出：世界空间位置
varying vec3 vNormal;       // 输出：世界空间法线
varying vec2 vUv;           // 输出：纹理坐标

void main() {
    // 1. 模型空间 → 世界空间
    vec4 worldPos = modelMatrix * vec4(position, 1.0);
    vWorldPos = worldPos.xyz;

    // 2. 世界空间 → 观察空间 → 裁剪空间
    gl_Position = projectionMatrix * viewMatrix * worldPos;

    // 3. 变换法线向量
    vNormal = normalize(normalMatrix * normal);

    // 4. 传递纹理坐标
    vUv = uv;
}
```

### 2.3 变换矩阵详解

#### 模型矩阵 (Model Matrix)

```javascript
// 组合变换：缩放 × 旋转 × 平移
const modelMatrix = mat4.create();
mat4.translate(modelMatrix, modelMatrix, [x, y, z]); // 平移
mat4.rotateY(modelMatrix, modelMatrix, rotationY); // 旋转
mat4.scale(modelMatrix, modelMatrix, [sx, sy, sz]); // 缩放
```

#### 视图矩阵 (View Matrix)

```javascript
// 相机变换
const viewMatrix = mat4.create();
mat4.lookAt(
    viewMatrix,
    [eyeX, eyeY, eyeZ], // 相机位置
    [centerX, centerY, centerZ], // 观察目标
    [upX, upY, upZ] // 上方向
);
```

#### 投影矩阵 (Projection Matrix)

```javascript
// 透视投影
const projectionMatrix = mat4.create();
mat4.perspective(
    projectionMatrix,
    fov, // 视野角度
    aspect, // 宽高比
    near, // 近裁剪面
    far // 远裁剪面
);
```

### 2.4 顶点着色器输出

-   **gl_Position**：必须输出，裁剪空间坐标
-   **varying 变量**：传递给片元着色器的插值数据

## 3. 图元装配阶段 (Primitive Assembly)

### 3.1 主要功能

将顶点着色器输出的顶点组装成图元（点、线、三角形）：

```javascript
// 不同的图元类型
gl.drawArrays(gl.POINTS, 0, vertexCount); // 点
gl.drawArrays(gl.LINES, 0, vertexCount); // 线段
gl.drawArrays(gl.LINE_STRIP, 0, vertexCount); // 线条
gl.drawArrays(gl.TRIANGLES, 0, vertexCount); // 三角形
gl.drawArrays(gl.TRIANGLE_STRIP, 0, vertexCount); // 三角形带
```

### 3.2 裁剪操作

在裁剪空间中进行视锥体裁剪：

```
裁剪条件：
-w ≤ x ≤ w
-w ≤ y ≤ w
-w ≤ z ≤ w

其中 (x, y, z, w) 是裁剪空间坐标
```

### 3.3 透视除法

将裁剪空间坐标转换为标准化设备坐标(NDC)：

```
NDC.x = clip.x / clip.w
NDC.y = clip.y / clip.w
NDC.z = clip.z / clip.w

结果范围：[-1, 1]
```

### 3.4 视口变换

将 NDC 坐标转换为屏幕坐标：

```javascript
// 设置视口
gl.viewport(x, y, width, height);

// 变换公式
screen.x = ((NDC.x + 1) * width) / 2 + x;
screen.y = ((NDC.y + 1) * height) / 2 + y;
screen.z = (NDC.z + 1) / 2; // 深度值 [0, 1]
```

## 4. 光栅化阶段 (Rasterization)

### 4.1 主要任务

将图元转换为像素片元(Fragment)：

1. **扫描转换**：确定图元覆盖的像素
2. **属性插值**：计算每个像素的属性值
3. **片元生成**：为每个像素生成片元

### 4.2 三角形光栅化算法

#### 边缘函数法

```glsl
// 判断点是否在三角形内
bool pointInTriangle(vec2 p, vec2 v0, vec2 v1, vec2 v2) {
    float e0 = (p.x - v1.x) * (v0.y - v1.y) - (p.y - v1.y) * (v0.x - v1.x);
    float e1 = (p.x - v2.x) * (v1.y - v2.y) - (p.y - v2.y) * (v1.x - v2.x);
    float e2 = (p.x - v0.x) * (v2.y - v0.y) - (p.y - v0.y) * (v2.x - v0.x);

    return (e0 >= 0 && e1 >= 0 && e2 >= 0) || (e0 <= 0 && e1 <= 0 && e2 <= 0);
}
```

#### 重心坐标插值

```glsl
// 计算重心坐标
vec3 barycentric(vec2 p, vec2 v0, vec2 v1, vec2 v2) {
    vec2 v0v1 = v1 - v0;
    vec2 v0v2 = v2 - v0;
    vec2 v0p = p - v0;

    float dot00 = dot(v0v2, v0v2);
    float dot01 = dot(v0v2, v0v1);
    float dot02 = dot(v0v2, v0p);
    float dot11 = dot(v0v1, v0v1);
    float dot12 = dot(v0v1, v0p);

    float invDenom = 1.0 / (dot00 * dot11 - dot01 * dot01);
    float u = (dot11 * dot02 - dot01 * dot12) * invDenom;
    float v = (dot00 * dot12 - dot01 * dot02) * invDenom;

    return vec3(1.0 - u - v, v, u);
}
```

### 4.3 属性插值

使用重心坐标插值顶点属性：

```glsl
// 插值计算
vec3 interpolatedNormal = bary.x * normal0 + bary.y * normal1 + bary.z * normal2;
vec2 interpolatedUV = bary.x * uv0 + bary.y * uv1 + bary.z * uv2;
vec3 interpolatedColor = bary.x * color0 + bary.y * color1 + bary.z * color2;
```

### 4.4 透视校正插值

对于透视投影，需要进行透视校正：

```glsl
// 透视校正插值
float w0_inv = 1.0 / w0;
float w1_inv = 1.0 / w1;
float w2_inv = 1.0 / w2;

float w_inv = bary.x * w0_inv + bary.y * w1_inv + bary.z * w2_inv;
vec2 correctedUV = (bary.x * uv0 * w0_inv + bary.y * uv1 * w1_inv + bary.z * uv2 * w2_inv) / w_inv;
```

## 5. 片元着色器阶段 (Fragment Shader)

### 5.1 主要职责

片元着色器对每个像素片元独立执行，主要完成：

1. **纹理采样**：从纹理中获取颜色信息
2. **光照计算**：计算像素级光照效果
3. **材质计算**：应用材质属性
4. **颜色输出**：计算最终像素颜色

### 5.2 基础片元着色器结构

```glsl
precision mediump float;

// 从顶点着色器接收的插值数据
varying vec3 vWorldPos;     // 世界空间位置
varying vec3 vNormal;       // 法线向量
varying vec2 vUv;           // 纹理坐标

// 统一变量
uniform sampler2D uTexture;     // 纹理
uniform vec3 uLightPos;         // 光源位置
uniform vec3 uViewPos;          // 观察者位置
uniform vec3 uLightColor;       // 光源颜色
uniform float uAmbientStrength; // 环境光强度

void main() {
    // 1. 纹理采样
    vec4 textureColor = texture2D(uTexture, vUv);

    // 2. 光照计算
    vec3 lightColor = calculateLighting();

    // 3. 最终颜色
    gl_FragColor = vec4(textureColor.rgb * lightColor, textureColor.a);
}
```

### 5.3 纹理采样详解

#### 基础纹理采样

```glsl
// 基础采样
vec4 color = texture2D(uTexture, vUv);

// 带偏移采样
vec4 color = texture2D(uTexture, vUv + vec2(0.1, 0.0));

// 多重纹理混合
vec4 tex1 = texture2D(uTexture1, vUv);
vec4 tex2 = texture2D(uTexture2, vUv);
vec4 blended = mix(tex1, tex2, 0.5);
```

#### 纹理过滤和包装

```javascript
// 设置纹理参数
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.REPEAT);
gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.REPEAT);
```

### 5.4 光照计算模型

#### Phong 光照模型

```glsl
vec3 calculatePhongLighting() {
    vec3 normal = normalize(vNormal);
    vec3 lightDir = normalize(uLightPos - vWorldPos);
    vec3 viewDir = normalize(uViewPos - vWorldPos);
    vec3 reflectDir = reflect(-lightDir, normal);

    // 环境光
    vec3 ambient = uAmbientStrength * uLightColor;

    // 漫反射
    float diff = max(dot(normal, lightDir), 0.0);
    vec3 diffuse = diff * uLightColor;

    // 镜面反射
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32.0);
    vec3 specular = spec * uLightColor;

    return ambient + diffuse + specular;
}
```

#### PBR 光照模型

```glsl
// 基于物理的渲染
vec3 calculatePBRLighting() {
    vec3 albedo = texture2D(uAlbedoMap, vUv).rgb;
    float metallic = texture2D(uMetallicMap, vUv).r;
    float roughness = texture2D(uRoughnessMap, vUv).r;
    vec3 normal = getNormalFromMap();

    vec3 N = normalize(normal);
    vec3 V = normalize(uViewPos - vWorldPos);
    vec3 L = normalize(uLightPos - vWorldPos);
    vec3 H = normalize(V + L);

    // 菲涅尔反射
    vec3 F0 = mix(vec3(0.04), albedo, metallic);
    vec3 F = fresnelSchlick(max(dot(H, V), 0.0), F0);

    // 法线分布函数
    float NDF = distributionGGX(N, H, roughness);

    // 几何函数
    float G = geometrySmith(N, V, L, roughness);

    // BRDF计算
    vec3 numerator = NDF * G * F;
    float denominator = 4.0 * max(dot(N, V), 0.0) * max(dot(N, L), 0.0) + 0.001;
    vec3 specular = numerator / denominator;

    vec3 kS = F;
    vec3 kD = vec3(1.0) - kS;
    kD *= 1.0 - metallic;

    float NdotL = max(dot(N, L), 0.0);
    return (kD * albedo / PI + specular) * uLightColor * NdotL;
}
```

### 5.5 高级片元着色器技术

#### 法线贴图

```glsl
vec3 getNormalFromMap() {
    vec3 tangentNormal = texture2D(uNormalMap, vUv).xyz * 2.0 - 1.0;

    vec3 Q1 = dFdx(vWorldPos);
    vec3 Q2 = dFdy(vWorldPos);
    vec2 st1 = dFdx(vUv);
    vec2 st2 = dFdy(vUv);

    vec3 N = normalize(vNormal);
    vec3 T = normalize(Q1 * st2.t - Q2 * st1.t);
    vec3 B = -normalize(cross(N, T));
    mat3 TBN = mat3(T, B, N);

    return normalize(TBN * tangentNormal);
}
```

#### 视差贴图

```glsl
vec2 parallaxMapping(vec2 texCoords, vec3 viewDir) {
    float height = texture2D(uHeightMap, texCoords).r;
    vec2 p = viewDir.xy / viewDir.z * (height * uHeightScale);
    return texCoords - p;
}
```

## 6. 输出合并阶段 (Output Merger)

### 6.1 主要功能

输出合并是渲染管线的最后阶段，负责：

1. **深度测试**：确定像素的可见性
2. **模板测试**：复杂的遮罩操作
3. **Alpha 混合**：透明度处理
4. **帧缓冲写入**：最终颜色输出

### 6.2 深度测试 (Depth Test)

#### 深度缓冲区

```javascript
// 启用深度测试
gl.enable(gl.DEPTH_TEST);
gl.depthFunc(gl.LESS); // 深度函数

// 清除深度缓冲区
gl.clear(gl.DEPTH_BUFFER_BIT);
```

#### 深度测试函数

```javascript
// 不同的深度测试函数
gl.depthFunc(gl.NEVER); // 永不通过
gl.depthFunc(gl.LESS); // 小于通过 (默认)
gl.depthFunc(gl.EQUAL); // 等于通过
gl.depthFunc(gl.LEQUAL); // 小于等于通过
gl.depthFunc(gl.GREATER); // 大于通过
gl.depthFunc(gl.NOTEQUAL); // 不等于通过
gl.depthFunc(gl.GEQUAL); // 大于等于通过
gl.depthFunc(gl.ALWAYS); // 总是通过
```

### 6.3 Alpha 混合 (Alpha Blending)

#### 混合方程

```
最终颜色 = 源颜色 × 源因子 + 目标颜色 × 目标因子
```

#### 常用混合模式

```javascript
// 启用混合
gl.enable(gl.BLEND);

// 标准Alpha混合
gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

// 加法混合
gl.blendFunc(gl.ONE, gl.ONE);

// 乘法混合
gl.blendFunc(gl.DST_COLOR, gl.ZERO);

// 自定义混合
gl.blendFuncSeparate(
    gl.SRC_ALPHA,
    gl.ONE_MINUS_SRC_ALPHA, // RGB混合
    gl.ONE,
    gl.ONE_MINUS_SRC_ALPHA // Alpha混合
);
```

### 6.4 模板测试 (Stencil Test)

#### 模板缓冲区操作

```javascript
// 启用模板测试
gl.enable(gl.STENCIL_TEST);

// 设置模板函数
gl.stencilFunc(
    gl.EQUAL, // 测试函数
    1, // 参考值
    0xff // 掩码
);

// 设置模板操作
gl.stencilOp(
    gl.KEEP, // 模板测试失败时
    gl.KEEP, // 模板测试通过但深度测试失败时
    gl.REPLACE // 两个测试都通过时
);
```

### 6.5 渲染状态管理

#### 面剔除

```javascript
// 启用面剔除
gl.enable(gl.CULL_FACE);
gl.cullFace(gl.BACK); // 剔除背面
gl.frontFace(gl.CCW); // 逆时针为正面
```

#### 多重采样抗锯齿

```javascript
// 启用多重采样
gl.enable(gl.SAMPLE_COVERAGE);
gl.sampleCoverage(1.0, false);
```

## 7. 完整渲染流程示例

### 7.1 渲染循环

```javascript
function render() {
    // 1. 清除缓冲区
    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);

    // 2. 设置渲染状态
    gl.enable(gl.DEPTH_TEST);
    gl.enable(gl.CULL_FACE);

    // 3. 绑定着色器程序
    gl.useProgram(program);

    // 4. 设置统一变量
    gl.uniformMatrix4fv(uModelMatrix, false, modelMatrix);
    gl.uniformMatrix4fv(uViewMatrix, false, viewMatrix);
    gl.uniformMatrix4fv(uProjectionMatrix, false, projectionMatrix);

    // 5. 绑定纹理
    gl.activeTexture(gl.TEXTURE0);
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.uniform1i(uTexture, 0);

    // 6. 绑定顶点数据
    gl.bindVertexArray(vao);

    // 7. 执行绘制
    gl.drawElements(gl.TRIANGLES, indexCount, gl.UNSIGNED_SHORT, 0);

    // 8. 下一帧
    requestAnimationFrame(render);
}
```

## 8. GPU 并行处理机制

### 8.1 SIMD 架构

GPU 采用 SIMD (Single Instruction, Multiple Data) 架构：

```
GPU核心组织：
├── 流多处理器 (SM/CU)
│   ├── 多个处理核心 (32-128个)
│   ├── 共享内存
│   ├── 纹理缓存
│   └── 常量缓存
└── 全局内存
```

### 8.2 着色器执行模型

#### 顶点着色器并行

```
顶点0  顶点1  顶点2  顶点3  ...  顶点N
  ↓      ↓      ↓      ↓           ↓
核心0  核心1  核心2  核心3  ...  核心M

每个核心独立执行相同的顶点着色器代码
```

#### 片元着色器并行 (Quad 执行)

```
2x2像素块 (Quad):
┌─────┬─────┐
│ P0  │ P1  │  ← 同时执行
├─────┼─────┤
│ P2  │ P3  │  ← 同时执行
└─────┴─────┘

这就是为什么dFdx/dFdy能工作的原因！
```

### 8.3 内存层次结构

#### GPU 内存类型

```
访问速度 (快 → 慢):
1. 寄存器 (Register)     - 每个线程私有
2. 共享内存 (Shared)     - 线程块内共享
3. 纹理缓存 (Texture)    - 只读，有缓存
4. 常量缓存 (Constant)   - 只读，广播
5. 全局内存 (Global)     - 读写，最慢
```

#### 内存访问模式

```glsl
// 好的访问模式 - 合并访问
vec4 color = texture2D(uTexture, vUv);

// 坏的访问模式 - 随机访问
vec4 color = texture2D(uTexture, vec2(random(), random()));
```

## 9. 性能优化策略

### 9.1 顶点优化

```javascript
// 减少顶点数据大小
const optimizedGeometry = {
    // 使用16位索引而不是32位
    index: { data: new Uint16Array(indices) },

    // 压缩顶点属性
    position: { size: 3, data: new Float32Array(positions) },
    normal: { size: 3, data: new Int8Array(normals) }, // 压缩法线
    uv: { size: 2, data: new Uint16Array(uvs) }, // 压缩UV
};
```

### 9.2 着色器优化

```glsl
// 避免分支
// 坏的写法
if (condition) {
    color = vec3(1.0, 0.0, 0.0);
} else {
    color = vec3(0.0, 1.0, 0.0);
}

// 好的写法
color = mix(vec3(0.0, 1.0, 0.0), vec3(1.0, 0.0, 0.0), float(condition));

// 预计算常量
const float PI = 3.14159265359;
const float INV_PI = 0.31830988618;

// 使用内置函数
float len = length(v);          // 而不是 sqrt(dot(v, v))
vec3 norm = normalize(v);       // 而不是 v / length(v)
```

### 9.3 渲染状态优化

```javascript
// 批处理相同状态的对象
function renderBatch(objects) {
    // 按材质分组
    const batches = groupByMaterial(objects);

    for (const batch of batches) {
        // 设置一次状态
        gl.useProgram(batch.program);
        gl.bindTexture(gl.TEXTURE_2D, batch.texture);

        // 渲染所有对象
        for (const obj of batch.objects) {
            gl.uniformMatrix4fv(uModelMatrix, false, obj.matrix);
            gl.drawElements(gl.TRIANGLES, obj.indexCount, gl.UNSIGNED_SHORT, 0);
        }
    }
}
```

## 10. 调试和分析工具

### 10.1 WebGL 调试

```javascript
// 启用WebGL调试上下文
const canvas = document.getElementById('canvas');
const gl = canvas.getContext('webgl', {
    antialias: false,
    alpha: false,
    depth: true,
    stencil: false,
    preserveDrawingBuffer: true, // 用于截图
});

// 检查WebGL错误
function checkGLError(operation) {
    const error = gl.getError();
    if (error !== gl.NO_ERROR) {
        console.error(`WebGL Error after ${operation}: ${error}`);
    }
}
```

### 10.2 性能监控

```javascript
// GPU时间测量
const ext = gl.getExtension('EXT_disjoint_timer_query');
if (ext) {
    const query = ext.createQueryEXT();

    ext.beginQueryEXT(ext.TIME_ELAPSED_EXT, query);
    // 执行渲染操作
    gl.drawElements(gl.TRIANGLES, indexCount, gl.UNSIGNED_SHORT, 0);
    ext.endQueryEXT(ext.TIME_ELAPSED_EXT);

    // 稍后检查结果
    if (ext.getQueryObjectEXT(query, ext.QUERY_RESULT_AVAILABLE_EXT)) {
        const timeElapsed = ext.getQueryObjectEXT(query, ext.QUERY_RESULT_EXT);
        console.log(`GPU time: ${timeElapsed / 1000000}ms`);
    }
}
```

## 11. 常见问题和解决方案

### 11.1 渲染问题

```javascript
// 问题：三角形不显示
// 解决方案：检查面剔除和绕序
gl.disable(gl.CULL_FACE); // 临时禁用面剔除
gl.frontFace(gl.CW); // 尝试不同的绕序

// 问题：深度测试不工作
// 解决方案：确保深度缓冲区存在
const gl = canvas.getContext('webgl', { depth: true });
gl.enable(gl.DEPTH_TEST);
gl.clear(gl.DEPTH_BUFFER_BIT);
```

### 11.2 着色器问题

```javascript
// 检查着色器编译错误
function checkShaderCompile(shader) {
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        const error = gl.getShaderInfoLog(shader);
        console.error('Shader compile error:', error);
        return false;
    }
    return true;
}

// 检查程序链接错误
function checkProgramLink(program) {
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        const error = gl.getProgramInfoLog(program);
        console.error('Program link error:', error);
        return false;
    }
    return true;
}
```

这个详细的渲染管线解析展示了从顶点数据到最终像素输出的完整过程，每个阶段都有其特定的职责和优化空间。理解这些底层机制对于编写高效的 WebGL 应用程序至关重要。
