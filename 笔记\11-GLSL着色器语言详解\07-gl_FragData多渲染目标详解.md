# gl_FragData 多渲染目标详解

## 概述

`gl_FragData` 是 WebGL 1.0 中用于**多渲染目标 (Multiple Render Targets, MRT)** 的内置变量，允许片段着色器在单次渲染过程中同时输出到多个颜色附件。这是实现高效渲染管线的重要技术，特别适用于延迟渲染、复杂后处理效果等高级图形技术。

## 1. 基本概念

### 1.1 什么是多渲染目标 (MRT)

多渲染目标允许一个片段着色器同时向多个纹理输出数据，而不需要多次渲染同一个场景。

```glsl
// 传统方法：需要多次渲染
// 第一次渲染 -> 输出颜色
// 第二次渲染 -> 输出法线
// 第三次渲染 -> 输出位置

// MRT方法：一次渲染同时输出多个结果
gl_FragData[0] = color;    // 颜色纹理
gl_FragData[1] = normal;   // 法线纹理
gl_FragData[2] = position; // 位置纹理
```

### 1.2 gl_FragData vs gl_FragColor

| 特性     | gl_FragColor | gl_FragData          |
| -------- | ------------ | -------------------- |
| 输出目标 | 单个         | 多个                 |
| 数据类型 | `vec4`       | `vec4[]` 数组        |
| 使用场景 | 基础渲染     | 高级渲染技术         |
| 性能影响 | 低           | 中等（需要更多带宽） |

## 2. 语法和使用方法

### 2.1 WebGL 1.0 语法

```glsl
#extension GL_EXT_draw_buffers : require

precision highp float;

uniform sampler2D tMap;
varying vec2 vUv;
varying vec3 vNormal;
varying vec3 vWorldPos;

void main() {
    // 同时输出到多个渲染目标
    gl_FragData[0] = texture2D(tMap, vUv);              // 颜色
    gl_FragData[1] = vec4(normalize(vNormal), 1.0);     // 法线
    gl_FragData[2] = vec4(vWorldPos, gl_FragCoord.z);   // 位置+深度
}
```

### 2.2 WebGL 2.0 对应语法

```glsl
#version 300 es
precision highp float;

uniform sampler2D tMap;
in vec2 vUv;
in vec3 vNormal;
in vec3 vWorldPos;

// 显式声明输出变量
out vec4 fragColor;
out vec4 fragNormal;
out vec4 fragPosition;

void main() {
    fragColor = texture(tMap, vUv);
    fragNormal = vec4(normalize(vNormal), 1.0);
    fragPosition = vec4(vWorldPos, gl_FragCoord.z);
}
```

### 2.3 JavaScript 端配置

```javascript
// 创建多渲染目标
const mrtTarget = new RenderTarget(gl, {
    width: 1024,
    height: 1024,
    color: 3, // 3个颜色附件对应 gl_FragData[0-2]

    // 可选：使用高精度格式
    type: gl.HALF_FLOAT,
    internalFormat: gl.RGBA16F,
});

// 渲染到多个目标
renderer.render({
    scene: scene,
    camera: camera,
    target: mrtTarget,
});

// 访问各个渲染结果
const colorTexture = mrtTarget.textures[0]; // gl_FragData[0]
const normalTexture = mrtTarget.textures[1]; // gl_FragData[1]
const positionTexture = mrtTarget.textures[2]; // gl_FragData[2]
```

## 3. 实际应用场景

### 3.1 延迟渲染 (Deferred Rendering)

延迟渲染是 MRT 最经典的应用场景，将几何渲染和光照计算分离。

```glsl
// G-Buffer 生成阶段
#extension GL_EXT_draw_buffers : require

precision highp float;

uniform sampler2D tAlbedo;
uniform sampler2D tNormal;
uniform sampler2D tMaterial;

varying vec2 vUv;
varying vec3 vNormal;
varying vec3 vWorldPos;
varying vec3 vTangent;
varying vec3 vBitangent;

void main() {
    // 采样纹理
    vec3 albedo = texture2D(tAlbedo, vUv).rgb;
    vec3 normalMap = texture2D(tNormal, vUv).rgb * 2.0 - 1.0;
    vec3 material = texture2D(tMaterial, vUv).rgb; // R=metallic, G=roughness, B=AO

    // 计算世界空间法线
    mat3 TBN = mat3(vTangent, vBitangent, vNormal);
    vec3 worldNormal = normalize(TBN * normalMap);

    // 输出到 G-Buffer
    gl_FragData[0] = vec4(albedo, material.r);           // 反照率 + 金属度
    gl_FragData[1] = vec4(worldNormal * 0.5 + 0.5, material.g); // 法线 + 粗糙度
    gl_FragData[2] = vec4(vWorldPos, material.b);        // 世界位置 + AO
    gl_FragData[3] = vec4(gl_FragCoord.z, 0, 0, 1);     // 深度信息
}
```

### 3.2 高级后处理效果

```glsl
// 同时生成多种后处理数据
#extension GL_EXT_draw_buffers : require

precision highp float;

uniform sampler2D tScene;
uniform vec2 uResolution;
uniform float uTime;

varying vec2 vUv;

// 亮度提取函数
vec3 extractBright(vec3 color, float threshold) {
    float brightness = dot(color, vec3(0.299, 0.587, 0.114));
    return color * step(threshold, brightness);
}

// 运动模糊向量计算
vec2 calculateVelocity() {
    // 简化的运动向量计算
    return vec2(sin(uTime), cos(uTime)) * 0.01;
}

void main() {
    vec4 sceneColor = texture2D(tScene, vUv);

    // 同时输出多种后处理数据
    gl_FragData[0] = sceneColor;                                    // 原始场景
    gl_FragData[1] = vec4(extractBright(sceneColor.rgb, 0.8), 1.0); // 亮度提取（用于bloom）
    gl_FragData[2] = vec4(calculateVelocity(), 0, 1);               // 运动向量（用于运动模糊）
    gl_FragData[3] = vec4(gl_FragCoord.z, 0, 0, 1);                // 深度信息（用于DOF）
}
```

### 3.3 阴影映射优化

```glsl
// 同时生成多种阴影数据
#extension GL_EXT_draw_buffers : require

precision highp float;

varying vec4 vShadowCoord;

// 深度打包函数
vec4 packDepth(float depth) {
    const vec4 bitShift = vec4(256.0*256.0*256.0, 256.0*256.0, 256.0, 1.0);
    const vec4 bitMask = vec4(0.0, 1.0/256.0, 1.0/256.0, 1.0/256.0);
    vec4 res = fract(depth * bitShift);
    res -= res.xxyz * bitMask;
    return res;
}

void main() {
    float depth = vShadowCoord.z / vShadowCoord.w;

    // 输出多种阴影格式
    gl_FragData[0] = vec4(depth, depth, depth, 1.0);        // 标准深度
    gl_FragData[1] = packDepth(depth);                      // 打包深度（提高精度）
    gl_FragData[2] = vec4(depth, depth*depth, 0, 1);        // VSM（方差阴影映射）
    gl_FragData[3] = vec4(depth, depth*depth, depth*depth*depth, 1); // MSM（矩阴影映射）
}
```

## 4. 性能优化策略

### 4.1 减少渲染次数

```javascript
// ❌ 低效：多次渲染
function renderTraditional() {
    // 渲染颜色
    renderer.render({ scene, camera, target: colorTarget });

    // 渲染法线
    renderer.render({ scene, camera, target: normalTarget });

    // 渲染位置
    renderer.render({ scene, camera, target: positionTarget });
}

// ✅ 高效：一次渲染
function renderMRT() {
    renderer.render({ scene, camera, target: mrtTarget });
    // 同时得到颜色、法线、位置三个结果
}
```

### 4.2 合理的数据打包

```glsl
// ❌ 浪费：每个通道存储单一数据
void main() {
    gl_FragData[0] = vec4(metallic, 0, 0, 1);     // 只用了R通道
    gl_FragData[1] = vec4(roughness, 0, 0, 1);    // 只用了R通道
    gl_FragData[2] = vec4(ao, 0, 0, 1);           // 只用了R通道
}

// ✅ 高效：紧凑的数据打包
void main() {
    gl_FragData[0] = vec4(albedo, metallic);       // RGB=反照率, A=金属度
    gl_FragData[1] = vec4(normal * 0.5 + 0.5, roughness); // RGB=法线, A=粗糙度
    gl_FragData[2] = vec4(worldPos, ao);           // RGB=位置, A=环境光遮蔽
}
```

### 4.3 条件渲染优化

```glsl
// 根据需要动态启用MRT
uniform bool uEnableMRT;
uniform int uOutputCount;

void main() {
    vec4 color = calculateColor();

    if (uEnableMRT) {
        gl_FragData[0] = color;

        if (uOutputCount > 1) {
            gl_FragData[1] = calculateNormal();
        }

        if (uOutputCount > 2) {
            gl_FragData[2] = calculatePosition();
        }
    } else {
        gl_FragColor = color; // 回退到单目标渲染
    }
}
```

## 5. 硬件限制和兼容性

### 5.1 硬件限制检查

```javascript
// 检查MRT支持情况
function checkMRTSupport(gl) {
    // 检查扩展支持
    const drawBuffersExt = gl.getExtension('WEBGL_draw_buffers');
    if (!drawBuffersExt) {
        console.warn('MRT not supported: WEBGL_draw_buffers extension missing');
        return false;
    }

    // 检查最大渲染目标数量
    const maxDrawBuffers = gl.getParameter(gl.MAX_DRAW_BUFFERS);
    const maxColorAttachments = gl.getParameter(gl.MAX_COLOR_ATTACHMENTS);

    console.log(`最大绘制缓冲区数: ${maxDrawBuffers}`);
    console.log(`最大颜色附件数: ${maxColorAttachments}`);

    // 检查纹理单元数量
    const maxTextureUnits = gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS);
    console.log(`最大纹理单元数: ${maxTextureUnits}`);

    return {
        supported: true,
        maxTargets: Math.min(maxDrawBuffers, maxColorAttachments),
        maxTextureUnits: maxTextureUnits,
    };
}

// 使用示例
const mrtInfo = checkMRTSupport(gl);
if (mrtInfo.supported) {
    console.log(`可以使用最多 ${mrtInfo.maxTargets} 个渲染目标`);
}
```

### 5.2 典型硬件限制

| 设备类型        | 最大 MRT 数量 | 典型限制             |
| --------------- | ------------- | -------------------- |
| 桌面 GPU (高端) | 8-16          | NVIDIA RTX, AMD RX   |
| 桌面 GPU (中端) | 4-8           | GTX 1060, RX 580     |
| 移动 GPU (高端) | 4-8           | Adreno 640, Mali-G76 |
| 移动 GPU (中端) | 4             | Adreno 530, Mali-G52 |
| WebGL 最低要求  | 4             | 规范保证的最小值     |

### 5.3 兼容性处理

```javascript
// 渐进式MRT支持
class MRTRenderer {
    constructor(gl) {
        this.gl = gl;
        this.mrtInfo = checkMRTSupport(gl);
        this.maxTargets = this.mrtInfo.maxTargets;
    }

    createRenderTarget(requestedTargets) {
        // 根据硬件能力调整目标数量
        const actualTargets = Math.min(requestedTargets, this.maxTargets);

        if (actualTargets < requestedTargets) {
            console.warn(`请求 ${requestedTargets} 个目标，但只能支持 ${actualTargets} 个`);
        }

        return new RenderTarget(this.gl, {
            color: actualTargets,
            width: 1024,
            height: 1024,
        });
    }

    // 根据可用目标数量选择渲染策略
    selectRenderingStrategy() {
        if (this.maxTargets >= 4) {
            return 'full-deferred'; // 完整延迟渲染
        } else if (this.maxTargets >= 2) {
            return 'light-deferred'; // 轻量延迟渲染
        } else {
            return 'forward'; // 前向渲染
        }
    }
}
```

## 6. 调试和性能分析

### 6.1 MRT 调试技巧

```glsl
// 调试着色器：可视化不同的输出
uniform int uDebugMode; // 0=正常, 1=颜色, 2=法线, 3=位置

void main() {
    vec4 color = calculateColor();
    vec4 normal = calculateNormal();
    vec4 position = calculatePosition();

    // 正常输出
    gl_FragData[0] = color;
    gl_FragData[1] = normal;
    gl_FragData[2] = position;

    // 调试模式：将选定的数据输出到所有目标
    if (uDebugMode == 1) {
        gl_FragData[0] = gl_FragData[1] = gl_FragData[2] = color;
    } else if (uDebugMode == 2) {
        gl_FragData[0] = gl_FragData[1] = gl_FragData[2] = normal;
    } else if (uDebugMode == 3) {
        gl_FragData[0] = gl_FragData[1] = gl_FragData[2] = position;
    }
}
```

### 6.2 性能监控

```javascript
// MRT性能监控
class MRTProfiler {
    constructor(gl) {
        this.gl = gl;
        this.timingExt = gl.getExtension('EXT_disjoint_timer_query');
        this.queries = [];
    }

    startTiming(label) {
        if (!this.timingExt) return;

        const query = this.gl.createQuery();
        this.gl.beginQuery(this.timingExt.TIME_ELAPSED_EXT, query);

        this.queries.push({ label, query, startTime: performance.now() });
    }

    endTiming() {
        if (!this.timingExt || this.queries.length === 0) return;

        this.gl.endQuery(this.timingExt.TIME_ELAPSED_EXT);
    }

    getResults() {
        const results = [];

        this.queries.forEach(({ label, query, startTime }) => {
            const available = this.gl.getQueryParameter(query, this.gl.QUERY_RESULT_AVAILABLE);

            if (available) {
                const timeElapsed = this.gl.getQueryParameter(query, this.gl.QUERY_RESULT);
                const cpuTime = performance.now() - startTime;

                results.push({
                    label,
                    gpuTime: timeElapsed / 1000000, // 转换为毫秒
                    cpuTime: cpuTime,
                });

                this.gl.deleteQuery(query);
            }
        });

        // 清除已处理的查询
        this.queries = this.queries.filter((q) => !this.gl.getQueryParameter(q.query, this.gl.QUERY_RESULT_AVAILABLE));

        return results;
    }
}

// 使用示例
const profiler = new MRTProfiler(gl);

// 测量MRT渲染性能
profiler.startTiming('MRT Render');
renderer.render({ scene, camera, target: mrtTarget });
profiler.endTiming();

// 获取结果
setTimeout(() => {
    const results = profiler.getResults();
    results.forEach((result) => {
        console.log(`${result.label}: GPU ${result.gpuTime.toFixed(2)}ms, CPU ${result.cpuTime.toFixed(2)}ms`);
    });
}, 100);
```

### 6.3 内存使用分析

```javascript
// MRT内存使用计算
function calculateMRTMemoryUsage(width, height, targetCount, format) {
    const formatSizes = {
        [gl.RGBA]: 4, // 4 bytes per pixel
        [gl.RGB]: 3, // 3 bytes per pixel
        [gl.RGBA16F]: 8, // 8 bytes per pixel (half float)
        [gl.RGBA32F]: 16, // 16 bytes per pixel (full float)
    };

    const bytesPerPixel = formatSizes[format] || 4;
    const pixelCount = width * height;
    const totalBytes = pixelCount * bytesPerPixel * targetCount;

    return {
        totalBytes,
        totalMB: totalBytes / (1024 * 1024),
        perTargetMB: totalBytes / targetCount / (1024 * 1024),
        breakdown: {
            width,
            height,
            pixelCount,
            bytesPerPixel,
            targetCount,
        },
    };
}

// 使用示例
const memoryUsage = calculateMRTMemoryUsage(1920, 1080, 4, gl.RGBA16F);
console.log(`MRT内存使用: ${memoryUsage.totalMB.toFixed(2)}MB`);
console.log(`每个目标: ${memoryUsage.perTargetMB.toFixed(2)}MB`);
```

## 7. 最佳实践总结

### 7.1 设计原则

1. **数据紧凑性**: 充分利用每个通道，避免浪费
2. **格式一致性**: 所有 MRT 目标使用相同的格式和类型
3. **渐进增强**: 根据硬件能力调整功能
4. **性能优先**: 在质量和性能之间找到平衡

### 7.2 常见错误避免

```glsl
// ❌ 错误：不一致的输出
void main() {
    gl_FragData[0] = vec4(color, 1.0);
    gl_FragData[1] = vec4(normal, 0.5); // alpha不一致
    // gl_FragData[2] 未设置 - 会产生未定义行为
}

// ✅ 正确：一致的输出
void main() {
    gl_FragData[0] = vec4(color, 1.0);
    gl_FragData[1] = vec4(normal, 1.0);
    gl_FragData[2] = vec4(position, 1.0);
}
```

### 7.3 性能优化清单

-   [ ] 检查硬件 MRT 支持能力
-   [ ] 使用合适的纹理格式（避免过度精度）
-   [ ] 紧凑的数据打包策略
-   [ ] 条件渲染减少不必要的计算
-   [ ] 定期性能监控和优化
-   [ ] 提供降级渲染路径

## 8. 实战示例：完整的延迟渲染管线

### 8.1 G-Buffer 生成

```javascript
// 创建G-Buffer渲染目标
const gBuffer = new RenderTarget(gl, {
    width: canvas.width,
    height: canvas.height,
    color: 4, // 4个G-Buffer纹理
    type: gl.HALF_FLOAT,
    internalFormat: gl.RGBA16F,
    depth: true,
});

// G-Buffer着色器
const gBufferShader = new Program(gl, {
    vertex: gBufferVertexShader,
    fragment: /* glsl */ `
        #extension GL_EXT_draw_buffers : require

        precision highp float;

        uniform sampler2D tAlbedo;
        uniform sampler2D tNormal;
        uniform sampler2D tMaterial;

        varying vec2 vUv;
        varying vec3 vNormal;
        varying vec3 vWorldPos;
        varying mat3 vTBN;

        void main() {
            // 采样纹理
            vec3 albedo = texture2D(tAlbedo, vUv).rgb;
            vec3 normalMap = texture2D(tNormal, vUv).rgb * 2.0 - 1.0;
            vec3 material = texture2D(tMaterial, vUv).rgb;

            // 计算世界法线
            vec3 worldNormal = normalize(vTBN * normalMap);

            // 输出G-Buffer
            gl_FragData[0] = vec4(albedo, material.r);           // 反照率+金属度
            gl_FragData[1] = vec4(worldNormal * 0.5 + 0.5, material.g); // 法线+粗糙度
            gl_FragData[2] = vec4(vWorldPos, material.b);        // 位置+AO
            gl_FragData[3] = vec4(gl_FragCoord.z, 0, 0, 1);     // 深度
        }
    `,
    uniforms: {
        tAlbedo: { value: albedoTexture },
        tNormal: { value: normalTexture },
        tMaterial: { value: materialTexture },
    },
});
```

### 8.2 光照计算

```javascript
// 光照pass着色器
const lightingShader = new Program(gl, {
    vertex: fullscreenVertexShader,
    fragment: /* glsl */ `
        precision highp float;

        uniform sampler2D tGBuffer0; // 反照率+金属度
        uniform sampler2D tGBuffer1; // 法线+粗糙度
        uniform sampler2D tGBuffer2; // 位置+AO
        uniform sampler2D tGBuffer3; // 深度

        uniform vec3 uCameraPos;
        uniform vec3 uLightPos;
        uniform vec3 uLightColor;

        varying vec2 vUv;

        // PBR光照计算函数
        vec3 calculatePBR(vec3 albedo, vec3 normal, vec3 worldPos,
                         float metallic, float roughness, float ao) {
            // 简化的PBR计算
            vec3 viewDir = normalize(uCameraPos - worldPos);
            vec3 lightDir = normalize(uLightPos - worldPos);
            vec3 halfDir = normalize(viewDir + lightDir);

            float NdotL = max(dot(normal, lightDir), 0.0);
            float NdotV = max(dot(normal, viewDir), 0.0);
            float NdotH = max(dot(normal, halfDir), 0.0);

            // 菲涅尔反射
            vec3 F0 = mix(vec3(0.04), albedo, metallic);
            vec3 F = F0 + (1.0 - F0) * pow(1.0 - NdotV, 5.0);

            // 分布函数 (简化)
            float alpha = roughness * roughness;
            float D = alpha / (3.14159 * pow(NdotH * NdotH * (alpha - 1.0) + 1.0, 2.0));

            // 几何函数 (简化)
            float G = NdotL * NdotV;

            // 最终颜色
            vec3 specular = D * F * G / max(4.0 * NdotL * NdotV, 0.001);
            vec3 diffuse = albedo / 3.14159 * (1.0 - F) * (1.0 - metallic);

            return (diffuse + specular) * uLightColor * NdotL * ao;
        }

        void main() {
            // 从G-Buffer读取数据
            vec4 gBuffer0 = texture2D(tGBuffer0, vUv);
            vec4 gBuffer1 = texture2D(tGBuffer1, vUv);
            vec4 gBuffer2 = texture2D(tGBuffer2, vUv);

            vec3 albedo = gBuffer0.rgb;
            float metallic = gBuffer0.a;

            vec3 normal = gBuffer1.rgb * 2.0 - 1.0;
            float roughness = gBuffer1.a;

            vec3 worldPos = gBuffer2.rgb;
            float ao = gBuffer2.a;

            // 计算最终光照
            vec3 finalColor = calculatePBR(albedo, normal, worldPos, metallic, roughness, ao);

            gl_FragColor = vec4(finalColor, 1.0);
        }
    `,
    uniforms: {
        tGBuffer0: { value: gBuffer.textures[0] },
        tGBuffer1: { value: gBuffer.textures[1] },
        tGBuffer2: { value: gBuffer.textures[2] },
        tGBuffer3: { value: gBuffer.textures[3] },
        uCameraPos: { value: camera.position },
        uLightPos: { value: [10, 10, 10] },
        uLightColor: { value: [1, 1, 1] },
    },
});
```

## 9. WebGL 1.0 vs 2.0 对比

| 特性       | WebGL 1.0             | WebGL 2.0             |
| ---------- | --------------------- | --------------------- |
| 语法       | `gl_FragData[i]`      | `out vec4 outputs[N]` |
| 扩展需求   | `GL_EXT_draw_buffers` | 内置支持              |
| 声明方式   | 内置变量              | 显式声明              |
| 类型检查   | 运行时                | 编译时                |
| 灵活性     | 固定数组              | 自定义名称            |
| 最大目标数 | 通常 4-8 个           | 通常 8-16 个          |

## 10. 总结

`gl_FragData` 是实现高效渲染管线的重要工具，通过多渲染目标技术可以：

-   **提高渲染效率**: 一次渲染生成多个结果
-   **支持高级技术**: 延迟渲染、复杂后处理等
-   **优化 GPU 利用**: 减少几何处理和状态切换开销

### 关键要点

1. **硬件兼容性**: 始终检查 MRT 支持情况
2. **数据设计**: 合理打包数据，充分利用每个通道
3. **性能监控**: 定期测量和优化渲染性能
4. **降级策略**: 为不支持 MRT 的设备提供备选方案

### 适用场景

-   **延迟渲染**: G-Buffer 生成和光照计算分离
-   **后处理效果**: 同时生成多种后处理数据
-   **阴影技术**: 多种阴影格式同时生成
-   **数据可视化**: 同时输出多个分析结果

在使用时需要注意硬件限制、格式兼容性和性能优化，合理设计数据布局和渲染策略，才能充分发挥 MRT 的优势。

---

_本笔记基于 WebGL 1.0 规范和 OGL 框架实际项目经验编写，涵盖了从基础概念到高级应用的完整知识体系。_
