// ========== 导入依赖模块 ==========
import { Mesh } from '../../core/Mesh.js'; // 网格基类
import { Program } from '../../core/Program.js'; // 着色器程序类
import { Geometry } from '../../core/Geometry.js'; // 几何体类
import { Vec3 } from '../../math/Vec3.js'; // 三维向量类
import { Mat3 } from '../../math/Mat3.js'; // 三维矩阵类

// ========== 全局临时向量（性能优化）==========
// 🔧 **性能优化说明**：
// 使用全局临时向量避免在循环中重复创建对象，减少垃圾回收压力。
// @__PURE__ 注释告诉打包工具这些是纯函数调用，可以安全优化。
const vA = /* @__PURE__ */ new Vec3(); // 三角形顶点A
const vB = /* @__PURE__ */ new Vec3(); // 三角形顶点B
const vC = /* @__PURE__ */ new Vec3(); // 三角形顶点C
const vCenter = /* @__PURE__ */ new Vec3(); // 三角形中心点
const vNormal = /* @__PURE__ */ new Vec3(); // 面法线向量

/**
 * 面法线辅助器类
 *
 * 🔬 **功能说明**：
 * 用于可视化3D模型的面法线向量，通过计算每个三角形面的法线方向，
 * 并在三角形中心绘制法线线段来显示面的朝向。
 *
 * 📐 **工作原理**：
 * 1. 遍历几何体的每个三角形面（每3个顶点组成一个面）
 * 2. 计算三角形的中心点（三个顶点的平均位置）
 * 3. 使用叉积计算面法线方向（垂直于三角形平面）
 * 4. 在中心点绘制沿法线方向的线段
 *
 * 🔍 **与VertexNormalsHelper的区别**：
 * - VertexNormalsHelper：显示每个顶点的法线（用于光照计算）
 * - FaceNormalsHelper：显示每个面的法线（用于面朝向判断）
 *
 * 💡 **使用场景**：
 * - 调试三角形面的朝向（正面/背面）
 * - 检查几何体的面法线是否正确
 * - 验证背面剔除设置
 * - 理解3D模型的几何结构
 */
export class FaceNormalsHelper extends Mesh {
    /**
     * 构造函数
     *
     * @param {Object} object - 要显示面法线的目标对象
     * @param {Object} options - 配置选项
     * @param {number} options.size - 法线线段的长度（默认0.1）
     * @param {Vec3} options.color - 法线线段的颜色（默认青色）
     * @param {Object} meshProps - 传递给Mesh基类的其他属性
     */
    constructor(object, { size = 0.1, color = new Vec3(0.15, 0.86, 0.86), ...meshProps } = {}) {
        const gl = object.gl;

        // ========== 获取几何数据 ==========
        const positionData = object.geometry.attributes.position.data; // 顶点位置数据
        const sizeData = new Float32Array([0, size]); // 线段起点和终点的尺寸值

        // ========== 处理索引缓冲区 ==========
        // 🔬 **索引处理说明**：
        // 有些几何体使用索引缓冲区来避免重复顶点数据，有些则直接存储顶点。
        // 需要统一处理这两种情况。
        const indexAttr = object.geometry.attributes.index;
        const getIndex = indexAttr ? (i) => indexAttr.data[i] : (i) => i; // 索引获取函数
        const numVertices = indexAttr ? indexAttr.data.length : Math.floor(positionData.length / 3);

        // ========== 计算面数量和创建数组 ==========
        const nNormals = Math.floor(numVertices / 3); // 面数量 = 顶点数 / 3
        const positionsArray = new Float32Array(nNormals * 2 * 3); // 每个面2个点，每点3坐标
        const normalsArray = new Float32Array(nNormals * 2 * 3); // 每个面2个点，每点3坐标
        const sizeArray = new Float32Array(nNormals * 2); // 每个面2个点，每点1尺寸

        // ========== 遍历每个三角形面 ==========
        // 🔬 **三角形处理流程**：
        // 每次处理3个顶点（i, i+1, i+2），它们组成一个三角形面
        for (let i = 0; i < numVertices; i += 3) {
            // ========== 获取三角形的三个顶点 ==========
            vA.fromArray(positionData, getIndex(i + 0) * 3); // 顶点A
            vB.fromArray(positionData, getIndex(i + 1) * 3); // 顶点B
            vC.fromArray(positionData, getIndex(i + 2) * 3); // 顶点C

            // ========== 计算三角形中心点 ==========
            // 📐 **中心点公式**：center = (A + B + C) / 3
            // 这是三角形重心的计算方法
            vCenter
                .add(vA, vB) // vCenter = vA + vB
                .add(vC) // vCenter = vCenter + vC = vA + vB + vC
                .multiply(1 / 3); // vCenter = (vA + vB + vC) / 3

            // ========== 计算面法线向量 ==========
            // 🔬 **法线计算原理**：
            // 1. 计算三角形的两条边向量：AB = vA - vB, CB = vC - vB
            // 2. 使用叉积计算垂直于平面的向量：normal = CB × AB
            // 3. 归一化得到单位法线向量
            //
            // 📐 **叉积的几何意义**：
            // 两个向量的叉积结果垂直于这两个向量所在的平面，
            // 方向遵循右手定则，大小等于平行四边形的面积。
            vA.sub(vA, vB); // vA = vA - vB (边向量AB)
            vC.sub(vC, vB); // vC = vC - vB (边向量CB)
            vNormal.cross(vC, vA).normalize(); // normal = CB × AB，然后归一化

            // ========== 设置线段数据 ==========
            // 为每个面创建一条从中心点出发的法线线段
            const i2 = i * 2; // 数组索引（每个面占用2个点的空间）

            // 设置线段起点和终点的位置（都是中心点，偏移在着色器中计算）
            positionsArray.set(vCenter, i2); // 起点位置
            positionsArray.set(vCenter, i2 + 3); // 终点位置

            // 设置线段起点和终点的法线方向（相同）
            normalsArray.set(vNormal, i2); // 起点法线
            normalsArray.set(vNormal, i2 + 3); // 终点法线

            // 设置线段起点和终点的尺寸值 [0, size]
            sizeArray.set(sizeData, (i / 3) * 2);
        }

        // ========== 创建线段几何体 ==========
        const geometry = new Geometry(gl, {
            position: { size: 3, data: positionsArray }, // 位置属性
            normal: { size: 3, data: normalsArray }, // 法线属性
            size: { size: 1, data: sizeArray }, // 尺寸属性
        });

        // ========== 创建着色器程序 ==========
        const program = new Program(gl, {
            vertex, // 顶点着色器（与VertexNormalsHelper相同）
            fragment, // 片段着色器（与VertexNormalsHelper相同）
            uniforms: {
                color: { value: color }, // 线段颜色（青色）
                worldNormalMatrix: { value: new Mat3() }, // 世界法线变换矩阵
                objectWorldMatrix: { value: object.worldMatrix }, // 对象世界变换矩阵
            },
        });

        // ========== 调用父类构造函数 ==========
        super(gl, { ...meshProps, mode: gl.LINES, geometry, program });

        // ========== 保存目标对象引用 ==========
        this.object = object;
    }

    /**
     * 绘制方法
     *
     * 🔬 **功能说明**：
     * 在每次绘制前更新世界法线变换矩阵，确保面法线方向正确。
     *
     * @param {Object} arg - 渲染参数
     */
    draw(arg) {
        // ========== 更新世界法线变换矩阵 ==========
        this.program.uniforms.worldNormalMatrix.value.getNormalMatrix(this.object.worldMatrix);

        // ========== 调用父类绘制方法 ==========
        super.draw(arg);
    }
}

// ========== 顶点着色器 ==========
// 🔬 **功能说明**：
// 与VertexNormalsHelper使用相同的着色器，但数据来源不同：
// - VertexNormalsHelper：position是顶点位置，normal是顶点法线
// - FaceNormalsHelper：position是面中心点，normal是面法线
//
// 📐 **渲染流程**：
// 1. 将面法线从对象空间变换到世界空间
// 2. 将面中心点从对象空间变换到世界空间
// 3. 根据size属性沿面法线方向偏移中心点位置
// 4. 将最终位置变换到裁剪空间
const vertex = /* glsl */ `
    // ========== 顶点属性 ==========
    attribute vec3 position;  // 面中心点位置（对象空间）
    attribute vec3 normal;    // 面法线方向（对象空间）
    attribute float size;     // 偏移距离（0=起点，指定值=终点）

    // ========== 变换矩阵 ==========
    uniform mat4 viewMatrix;        // 视图矩阵
    uniform mat4 projectionMatrix;  // 投影矩阵
    uniform mat4 objectWorldMatrix; // 对象世界矩阵
    uniform mat3 worldNormalMatrix; // 世界法线矩阵

    void main() {
        // ========== 面法线变换和缩放 ==========
        // 🔬 **面法线处理**：
        // 面法线是通过叉积计算得到的，表示三角形平面的垂直方向。
        // 需要正确变换到世界空间以保持几何意义。
        vec3 n = normalize(worldNormalMatrix * normal) * size;

        // ========== 面中心点变换 ==========
        // 将三角形中心点从对象空间变换到世界空间
        vec3 p = (objectWorldMatrix * vec4(position, 1.0)).xyz;

        // ========== 最终位置计算 ==========
        // 🔬 **线段生成**：
        // - 当size=0时：显示面中心点（线段起点）
        // - 当size=指定值时：沿面法线方向偏移（线段终点）
        // 这样形成从面中心出发、垂直于面的法线线段
        gl_Position = projectionMatrix * viewMatrix * vec4(p + n, 1.0);
    }
`;

// ========== 片段着色器 ==========
// 🔬 **功能说明**：
// 与VertexNormalsHelper使用相同的片段着色器，渲染统一颜色的线段。
// 默认使用青色来区分面法线和顶点法线（顶点法线通常用洋红色）。
//
// 🎨 **颜色约定**：
// - 面法线：青色 (0.15, 0.86, 0.86) - 冷色调，表示面的几何属性
// - 顶点法线：洋红色 (0.86, 0.16, 0.86) - 暖色调，表示顶点的光照属性
const fragment = /* glsl */ `
    precision highp float;

    // ========== 统一变量 ==========
    uniform vec3 color;     // 线段颜色（默认青色）

    void main() {
        // ========== 输出固定颜色 ==========
        // 面法线线段使用统一的青色，便于与顶点法线区分
        gl_FragColor = vec4(color, 1.0);
    }
`;
