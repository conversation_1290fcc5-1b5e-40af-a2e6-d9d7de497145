<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <!-- 禁用用户缩放，确保射线投射的鼠标坐标计算准确性 -->
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Projection and Raycasting</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <!-- 射线投射和投影示例：演示3D场景中的鼠标拾取技术 -->
        <div class="Info">Projection and Raycasting</div>
        <script type="module">
            // 导入OGL框架组件
            // Raycast: 射线投射类，用于3D场景中的对象拾取和碰撞检测
            import { Renderer, Camera, Transform, Program, Mesh, Vec2, Plane, Sphere, Box, Orbit, Raycast } from '../src/index.js';

            // 顶点着色器：处理3D几何体的基本变换和法线计算
            const vertex = /* glsl */ `
                // 输入属性：顶点数据
                attribute vec3 position; // 顶点位置（模型空间）
                attribute vec3 normal;   // 顶点法线（模型空间）

                // 变换矩阵：用于坐标空间转换
                uniform mat4 modelViewMatrix;  // 模型-视图矩阵
                uniform mat4 projectionMatrix; // 投影矩阵
                uniform mat3 normalMatrix;     // 法线变换矩阵

                // 输出到片段着色器的插值数据
                varying vec3 vNormal; // 世界空间法线

                void main() {
                    // 将法线从模型空间变换到世界空间
                    vNormal = normalize(normalMatrix * normal);

                    // 标准MVP变换：模型空间 → 视图空间 → 裁剪空间
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // 片段着色器：实现射线命中状态的视觉反馈
            const fragment = /* glsl */ `
                precision highp float;

                // 射线命中状态：0.0 = 未命中，1.0 = 命中
                uniform float uHit;

                // 从顶点着色器接收的插值法线
                varying vec3 vNormal;

                void main() {
                    // 重新归一化插值后的法线向量
                    vec3 normal = normalize(vNormal);

                    // 简单的方向光照计算
                    // 光源方向：左上前方，创造自然的照明效果
                    float lighting = dot(normal, normalize(vec3(-0.3, 0.8, 0.6)));

                    // 根据射线命中状态混合颜色
                    // 未命中：蓝色 (0.2, 0.8, 1.0)
                    // 命中：粉红色 (1.0, 0.2, 0.8)
                    vec3 color = mix(vec3(0.2, 0.8, 1.0), vec3(1.0, 0.2, 0.8), uHit);

                    // 最终颜色 = 基础颜色 + 光照贡献
                    gl_FragColor.rgb = color + lighting * 0.1;
                    gl_FragColor.a = 1.0;
                }
            `;

            {
                // === 渲染器和相机设置 ===
                const renderer = new Renderer({ dpr: 2 });
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);
                gl.clearColor(1, 1, 1, 1); // 白色背景，便于观察射线命中效果

                // 创建透视相机，设置在合适的观察位置
                const camera = new Camera(gl);
                camera.position.set(2, 1, 5); // 右上后方位置，便于观察所有几何体

                // 添加轨道控制器，允许用户旋转视角观察射线投射效果
                const orbit = new Orbit(camera);

                // === 窗口大小调整处理 ===
                function resize() {
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    // 更新相机宽高比，确保射线投射的屏幕坐标计算正确
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize();

                // === 场景根节点 ===
                const scene = new Transform();

                // === 几何体创建 ===
                // 创建三种不同的几何体用于测试射线投射
                const planeGeometry = new Plane(gl); // 平面几何体
                const sphereGeometry = new Sphere(gl); // 球体几何体
                const cubeGeometry = new Box(gl); // 立方体几何体

                // === 着色器程序设置 ===
                const program = new Program(gl, {
                    vertex,
                    fragment,
                    cullFace: false, // 禁用背面剔除，确保从任何角度都能看到几何体
                    uniforms: {
                        // uHit uniform控制射线命中状态的视觉反馈
                        uHit: { value: 0 }, // 0 = 未命中，1 = 命中
                    },
                });

                // === 网格对象创建和定位 ===
                // 创建平面网格，位于上方
                const plane = new Mesh(gl, { geometry: planeGeometry, program });
                plane.position.set(0, 1.3, 0); // 向上偏移，避免重叠
                plane.setParent(scene);

                // 创建球体网格，位于中心
                const sphere = new Mesh(gl, { geometry: sphereGeometry, program });
                sphere.setParent(scene); // 保持在原点位置

                // 创建立方体网格，位于下方
                const cube = new Mesh(gl, { geometry: cubeGeometry, program });
                cube.position.set(0, -1.3, 0); // 向下偏移，避免重叠
                cube.setParent(scene);

                // === 动态Uniform更新机制 ===
                // 为每个网格分配更新函数，使它们能够共享同一个着色器程序
                // 但在渲染前动态更新各自的uniform值，实现独立的视觉状态
                function updateHitUniform({ mesh }) {
                    // 根据网格的命中状态更新uHit uniform
                    // 这个函数在每个网格渲染前被调用
                    program.uniforms.uHit.value = mesh.isHit ? 1 : 0;
                }

                // 为每个网格注册渲染前回调函数
                plane.onBeforeRender(updateHitUniform);
                sphere.onBeforeRender(updateHitUniform);
                cube.onBeforeRender(updateHitUniform);

                // === 渲染循环 ===
                requestAnimationFrame(update);
                function update() {
                    requestAnimationFrame(update);

                    // 更新轨道控制器（处理用户交互）
                    orbit.update();

                    // 渲染场景
                    renderer.render({ scene, camera });
                }

                // === 射线投射系统初始化 ===

                // 鼠标位置向量：存储归一化的设备坐标(NDC)
                // NDC范围：x∈[-1,1], y∈[-1,1]，原点在屏幕中心
                const mouse = new Vec2();

                // 创建射线投射对象
                // 射线投射是3D拾取的核心技术，通过从相机位置发射射线
                // 来检测射线与3D对象的交点
                const raycast = new Raycast();

                // 定义要进行射线测试的网格数组
                // 射线投射将按顺序测试这些对象
                const meshes = [plane, sphere, cube];

                // === 碰撞检测优化配置 ===
                // 默认情况下，raycast.intersectBounds() 使用轴对齐包围盒(AABB)进行快速碰撞检测
                // 对于球体，使用包围球更精确，通过设置 'raycast' 属性来指定
                // 'sphere': 使用包围球检测（更适合球形对象）
                // 'box' 或 undefined: 使用包围盒检测（默认，适合大多数对象）
                sphere.geometry.raycast = 'sphere';

                // === 事件监听器设置 ===
                // 等待页面完全加载后再添加事件监听器
                // 这确保了DOM元素和WebGL上下文都已准备就绪
                window.addEventListener(
                    'load',
                    () => {
                        // 添加鼠标移动事件监听器（桌面设备）
                        document.addEventListener('mousemove', move, false);
                        // 添加触摸移动事件监听器（移动设备）
                        document.addEventListener('touchmove', move, false);
                    },
                    false
                );

                /**
                 * 鼠标/触摸移动事件处理函数
                 *
                 * 射线投射的核心流程：
                 * 1. 屏幕坐标 → 归一化设备坐标(NDC)
                 * 2. NDC → 世界空间射线
                 * 3. 射线与3D对象求交
                 * 4. 更新视觉反馈
                 */
                function move(e) {
                    // === 坐标转换：屏幕空间 → 归一化设备坐标 ===
                    // 将屏幕像素坐标转换为NDC坐标系
                    // NDC: x∈[-1,1], y∈[-1,1]，原点在屏幕中心
                    //
                    // X轴转换：(pixel_x / width) * 2 - 1
                    // Y轴转换：(1 - pixel_y / height) * 2 - 1 (注意Y轴翻转)
                    mouse.set(
                        2.0 * (e.x / renderer.width) - 1.0, // X: [0,width] → [-1,1]
                        2.0 * (1.0 - e.y / renderer.height) - 1.0 // Y: [0,height] → [1,-1] → [-1,1]
                    );

                    // === 射线生成 ===
                    // 根据相机参数和鼠标位置生成世界空间射线
                    // 射线起点：相机位置
                    // 射线方向：从相机指向鼠标在近平面上对应点的方向
                    raycast.castMouse(camera, mouse);

                    // === 重置命中状态 ===
                    // 清除上一帧的命中状态，准备新的检测
                    meshes.forEach((mesh) => (mesh.isHit = false));

                    // === 射线-边界框相交测试 ===
                    // intersectBounds() 执行快速的边界体积相交测试
                    // 返回按距离排序的相交网格数组（近到远）
                    // 这是一种高效的粗略检测方法
                    const hits = raycast.intersectBounds(meshes);

                    // === 高精度几何相交测试（可选）===
                    // 如果需要更精确的相交信息（UV坐标、法线等），可以使用：
                    // const hits = raycast.intersectMeshes(meshes, {
                    //     cullFace: true,        // 启用背面剔除优化
                    //     maxDistance: 10,       // 最大检测距离限制
                    //     includeUV: true,       // 包含UV坐标信息
                    //     includeNormal: true,   // 包含交点法线信息
                    // });
                    //
                    // 访问详细交点信息：
                    // if (hits.length) {
                    //     console.log('UV坐标:', hits[0].hit.uv);
                    //     console.log('交点法线:', hits[0].hit.normal);
                    //     console.log('交点距离:', hits[0].hit.distance);
                    // }

                    // === 更新视觉反馈 ===
                    // 将命中的网格标记为已命中状态
                    // 这将触发着色器中的颜色变化
                    hits.forEach((mesh) => (mesh.isHit = true));
                }
            }
        </script>
    </body>
</html>
