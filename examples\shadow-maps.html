<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Shadow maps</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info">Shadow maps. Model by Google Poly</div>
        <script type="module">
            // 导入 OGL 框架的核心模块
            // Shadow: 阴影映射系统的核心类
            // Camera: 用于创建光源相机和主相机
            // Plane: 用于创建地面几何体
            import { Renderer, Camera, Transform, Texture, Program, Geometry, Mesh, Orbit, Plane, Shadow } from '../src/index.js';

            // ========================================
            // 阴影映射顶点着色器
            // ========================================
            // 功能：计算顶点在光源空间中的位置，用于阴影比较
            const vertexColor = /* glsl */ `
                // 顶点属性
                attribute vec3 position;    // 顶点位置
                attribute vec2 uv;          // 纹理坐标

                // 主相机变换矩阵
                uniform mat4 modelMatrix;       // 模型矩阵
                uniform mat4 modelViewMatrix;   // 模型视图矩阵
                uniform mat4 projectionMatrix;  // 投影矩阵

                // 光源相机变换矩阵 (用于阴影映射)
                uniform mat4 shadowViewMatrix;      // 光源视图矩阵
                uniform mat4 shadowProjectionMatrix; // 光源投影矩阵

                // 传递给片段着色器的变量
                varying vec2 vUv;           // 纹理坐标
                varying vec4 vLightNDC;     // 光源空间中的NDC坐标

                // 深度缩放矩阵：将NDC坐标范围从 [-1,1] 转换到 [0,1]
                // 这是阴影映射的标准变换，因为深度纹理的值域是 [0,1]
                const mat4 depthScaleMatrix = mat4(
                    0.5, 0.0, 0.0, 0.0,    // X: [-1,1] -> [0,1]
                    0.0, 0.5, 0.0, 0.0,    // Y: [-1,1] -> [0,1]
                    0.0, 0.0, 0.5, 0.0,    // Z: [-1,1] -> [0,1]
                    0.5, 0.5, 0.5, 1.0     // 平移分量
                );

                void main() {
                    // 传递纹理坐标
                    vUv = uv;

                    // 计算顶点在光源空间中的NDC坐标
                    // 这个坐标将用于在片段着色器中查询阴影贴图
                    // 变换顺序：模型空间 -> 世界空间 -> 光源视图空间 -> 光源投影空间 -> [0,1]范围
                    vLightNDC = depthScaleMatrix * shadowProjectionMatrix * shadowViewMatrix * modelMatrix * vec4(position, 1.0);

                    // 计算顶点在主相机空间中的最终位置
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // ========================================
            // 阴影映射片段着色器
            // ========================================
            // 功能：通过比较深度值来确定像素是否在阴影中
            const fragmentColor = /* glsl */ `
                precision highp float;

                // 纹理采样器
                uniform sampler2D tMap;     // 物体的颜色纹理
                uniform sampler2D tShadow;  // 阴影贴图 (深度纹理)

                // 从顶点着色器接收的数据
                varying vec2 vUv;           // 纹理坐标
                varying vec4 vLightNDC;     // 光源空间中的NDC坐标

                // RGBA深度解包函数
                // 功能：将RGBA格式编码的深度值解包为浮点数
                // 这是因为某些平台不支持浮点深度纹理，需要用RGBA来编码深度
                float unpackRGBA (vec4 v) {
                    // 使用点积来重建32位浮点深度值
                    // 每个通道代表不同的精度位：R(1), G(1/255), B(1/65025), A(1/16581375)
                    return dot(v, 1.0 / vec4(1.0, 255.0, 65025.0, 16581375.0));
                }

                void main() {
                    // 采样物体的基础颜色
                    vec3 tex = texture2D(tMap, vUv).rgb;

                    // 执行透视除法，获得光源空间中的标准化坐标
                    // vLightNDC.w 包含了透视投影的深度信息
                    vec3 lightPos = vLightNDC.xyz / vLightNDC.w;

                    // ========================================
                    // 阴影映射核心算法
                    // ========================================

                    // 深度偏移：防止阴影痤疮 (shadow acne)
                    // 由于浮点精度问题，物体可能会给自己投射阴影
                    float bias = 0.001;
                    float depth = lightPos.z - bias;  // 当前像素在光源空间中的深度

                    // 从阴影贴图中采样存储的深度值
                    // lightPos.xy 是光源空间中的纹理坐标 [0,1]
                    float occluder = unpackRGBA(texture2D(tShadow, lightPos.xy));

                    // 深度比较：判断当前像素是否在阴影中
                    // 如果阴影贴图中的深度值小于当前深度，说明有遮挡物，像素在阴影中
                    // step(depth, occluder): 如果 depth <= occluder 返回1.0，否则返回0.0
                    // mix(0.2, 1.0, ...): 在阴影中时亮度为0.2，不在阴影中时亮度为1.0
                    float shadow = mix(0.2, 1.0, step(depth, occluder));

                    // 应用阴影到最终颜色
                    gl_FragColor.rgb = tex * shadow;
                    gl_FragColor.a = 1.0;
                }
            `;

            // ========================================
            // 主程序：阴影映射演示应用
            // ========================================
            {
                // 创建渲染器，使用2倍像素密度提高画质
                const renderer = new Renderer({ dpr: 2 });
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);

                // 设置白色背景，便于观察阴影效果
                gl.clearColor(1, 1, 1, 1);

                // 创建主相机 (观察者视角)
                const camera = new Camera(gl, { fov: 35 });
                camera.position.set(5, 4, 10); // 相机位置：右上后方

                // 添加轨道控制器，允许用户交互
                const controls = new Orbit(camera);

                // 窗口大小调整处理
                function resize() {
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize();

                // 创建场景根节点
                const scene = new Transform();

                // ========================================
                // 光源相机配置 (阴影映射的关键)
                // ========================================

                // 可以在正交投影和透视投影之间切换：
                // - 正交投影 (left/right/bottom/top): 模拟平行光 (如太阳光)
                // - 透视投影 (fov): 模拟点光源 (如聚光灯)
                const light = new Camera(gl, {
                    // 正交投影参数 (当前使用)
                    left: -3, // 左边界
                    right: 3, // 右边界
                    bottom: -3, // 下边界
                    top: 3, // 上边界

                    // 透视投影参数 (注释掉)
                    // fov: 30, // 视野角度，取消注释可切换到聚光灯效果

                    // 深度范围 (两种投影都需要)
                    near: 1, // 近裁剪面
                    far: 20, // 远裁剪面
                });

                // 设置光源位置和朝向
                light.position.set(3, 10, 3); // 光源位置：右上方
                light.lookAt([0, 0, 0]); // 光源朝向：场景中心

                // 创建阴影系统实例
                // Shadow 类会自动创建深度纹理和帧缓冲区
                const shadow = new Shadow(gl, { light });

                // 初始化场景对象
                addAirplane();
                addGround();

                // ========================================
                // 飞机模型创建函数
                // ========================================
                let airplane; // 飞机网格引用，用于动画
                async function addAirplane() {
                    // 创建飞机纹理
                    const texture = new Texture(gl);
                    const img = new Image();
                    img.onload = () => (texture.image = img);
                    img.src = 'assets/airplane.jpg';

                    // 创建着色器程序
                    // 使用支持阴影映射的顶点和片段着色器
                    const program = new Program(gl, {
                        vertex: vertexColor, // 阴影映射顶点着色器
                        fragment: fragmentColor, // 阴影映射片段着色器
                        uniforms: {
                            tMap: { value: texture }, // 飞机纹理
                            // tShadow 会由 Shadow 系统自动注入
                        },
                        cullFace: false, // 禁用面剔除，确保飞机内部也可见
                    });

                    // 异步加载飞机几何数据
                    const data = await (await fetch(`assets/airplane.json`)).json();

                    // 创建飞机几何体
                    const geometry = new Geometry(gl, {
                        position: { size: 3, data: new Float32Array(data.position) }, // 顶点位置
                        uv: { size: 2, data: new Float32Array(data.uv) }, // 纹理坐标
                        normal: { size: 3, data: new Float32Array(data.normal) }, // 顶点法线
                    });

                    // 创建飞机网格
                    const mesh = new Mesh(gl, { geometry, program });
                    mesh.setParent(scene);

                    // 关键步骤：将网格添加到阴影系统
                    // 这会让飞机既能投射阴影，也能接收阴影
                    // Shadow 系统会自动注入必要的 uniform 变量
                    shadow.add({ mesh });

                    // 保存飞机引用用于动画
                    airplane = mesh;
                }

                // ========================================
                // 地面创建函数
                // ========================================
                function addGround() {
                    // 创建地面纹理 (水面纹理)
                    const texture = new Texture(gl);
                    const img = new Image();
                    img.onload = () => (texture.image = img);
                    img.src = 'assets/water.jpg';

                    // 创建着色器程序 (与飞机使用相同的阴影映射着色器)
                    const program = new Program(gl, {
                        vertex: vertexColor, // 阴影映射顶点着色器
                        fragment: fragmentColor, // 阴影映射片段着色器
                        uniforms: {
                            tMap: { value: texture }, // 地面纹理
                            // tShadow 会由 Shadow 系统自动注入
                        },
                        cullFace: false, // 禁用面剔除
                    });

                    // 创建平面几何体 (地面)
                    const geometry = new Plane(gl);

                    // 创建地面网格
                    const mesh = new Mesh(gl, { geometry, program });
                    mesh.setParent(scene);

                    // 将地面添加到阴影系统
                    // 地面主要用于接收阴影，展示飞机的投影
                    shadow.add({ mesh });

                    // 地面变换设置
                    mesh.rotation.x = Math.PI / 2; // 旋转90度，使平面水平
                    mesh.scale.set(6); // 放大6倍，创建足够大的地面
                    mesh.position.y = -3; // 向下移动3个单位
                }

                // ========================================
                // 渲染循环：阴影映射渲染管线
                // ========================================
                requestAnimationFrame(update);
                function update(t) {
                    requestAnimationFrame(update);

                    // 更新相机控制器
                    controls.update();

                    // 飞机动画：创建自然的飞行效果
                    if (airplane) {
                        // Z轴位置：前后摆动
                        airplane.position.z = Math.sin(t * 0.001);

                        // X轴旋转：俯仰运动 (pitch)
                        airplane.rotation.x = Math.sin(t * 0.001 + 2) * 0.1;

                        // Y轴旋转：偏航运动 (yaw)
                        airplane.rotation.y = Math.sin(t * 0.001 - 4) * -0.1;
                    }

                    // 阴影映射渲染分为两个步骤：

                    // 第一步：从光源视角渲染深度信息到阴影贴图
                    // 这会生成场景的深度纹理，记录每个像素到光源的距离
                    shadow.render({ scene });

                    // 第二步：从主相机视角渲染最终场景
                    // 在片段着色器中使用阴影贴图进行深度比较，生成阴影效果
                    renderer.render({ scene, camera });
                }
            }
        </script>
    </body>
</html>
