<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • GPGPU Particles (General-Purpose computing on Graphics Processing Units)</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info">GPGPU Particles (General-Purpose computing on Graphics Processing Units)</div>
        <script type="module">
            // 导入OGL库的核心组件
            // Renderer: WebGL渲染器
            // Camera: 相机控制
            // Geometry: 几何体定义
            // Program: 着色器程序
            // Mesh: 网格对象
            // Vec2: 二维向量
            // GPGPU: GPU通用计算类，用于在GPU上进行非图形计算
            import { Renderer, Camera, Geometry, Program, Mesh, Vec2, GPGPU } from '../src/index.js';

            // 粒子渲染顶点着色器 - 用于渲染每个粒子点
            const vertex = /* glsl */ `
                attribute vec2 coords;      // 纹理坐标，用于从GPGPU纹理中采样数据
                attribute vec4 random;      // 每个粒子的随机值（静态属性）

                uniform float uTime;        // 时间uniform
                uniform sampler2D tPosition; // 位置纹理（由GPGPU计算更新）
                uniform sampler2D tVelocity; // 速度纹理（由GPGPU计算更新）

                varying vec4 vRandom;       // 传递给片段着色器的随机值
                varying vec4 vVelocity;     // 传递给片段着色器的速度值

                void main() {
                    vRandom = random;

                    // 从纹理中获取位置数据，而不是从顶点属性
                    // 这是GPGPU的核心概念：数据存储在纹理中，由GPU计算更新
                    vec4 position = texture2D(tPosition, coords);
                    vVelocity = texture2D(tVelocity, coords);

                    // 添加微妙的随机振荡，使粒子永远不会完全静止
                    // 使用正弦函数和随机值创建有机的运动效果
                    position.xy += sin(vec2(uTime) * vRandom.wy + vRandom.xz * 6.28) * vRandom.zy * 0.1;

                    // 设置粒子在屏幕上的位置
                    gl_Position = vec4(position.xy, 0, 1);

                    // 根据随机值设置粒子大小（2.0到15.0像素之间）
                    gl_PointSize = mix(2.0, 15.0, vRandom.x);

                    // 根据移动速度动态调整粒子大小 - 移动越快，粒子越大
                    gl_PointSize *= 1.0 + min(1.0, length(vVelocity.xy));
                }
            `;

            // 粒子渲染片段着色器 - 定义每个粒子的外观
            const fragment = /* glsl */ `
                precision highp float;

                varying vec4 vRandom;       // 从顶点着色器传入的随机值
                varying vec4 vVelocity;     // 从顶点着色器传入的速度值

                void main() {

                    // 创建圆形粒子形状
                    // gl_PointCoord是点精灵的内置坐标（0-1范围）
                    // 计算距离中心的距离，超过0.5的像素被丢弃，形成圆形
                    if (step(0.5, length(gl_PointCoord.xy - 0.5)) > 0.0) discard;

                    // 基于随机值生成粒子颜色
                    // 使用随机值的不同分量创建多样化的颜色
                    vec3 color = vec3(vRandom.zy, 1.0) * mix(0.7, 2.0, vRandom.w);

                    // 根据速度调整颜色：静止时渐变为白色，移动时显示彩色
                    // 使用缓动曲线创建平滑的颜色过渡效果
                    gl_FragColor.rgb = mix(vec3(1), color, 1.0 - pow(1.0 - smoothstep(0.0, 0.7, length(vVelocity.xy)), 2.0));

                    gl_FragColor.a = 1.0;
                }
            `;

            // 位置更新片段着色器 - GPGPU计算，用于更新粒子位置
            const positionFragment = /* glsl */ `
                precision highp float;

                uniform float uTime;            // 时间uniform（未在此着色器中使用）
                uniform sampler2D tVelocity;    // 速度纹理

                // GPGPU通道的默认纹理uniform是'tMap'
                // 可以使用textureUniform参数来更新
                // 这里tMap包含当前的位置数据
                uniform sampler2D tMap;

                varying vec2 vUv;               // 纹理坐标

                void main() {
                    // 从纹理中读取当前位置和速度
                    vec4 position = texture2D(tMap, vUv);
                    vec4 velocity = texture2D(tVelocity, vUv);

                    // 根据速度更新位置：新位置 = 当前位置 + 速度 * 时间步长
                    position.xy += velocity.xy * 0.01;

                    // 边界处理：保持粒子在屏幕范围内（-1到1）
                    vec2 limits = vec2(1);
                    // 如果粒子超出左边界或下边界，将其移动到右边界或上边界
                    position.xy += (1.0 - step(-limits.xy, position.xy)) * limits.xy * 2.0;
                    // 如果粒子超出右边界或上边界，将其移动到左边界或下边界
                    position.xy -= step(limits.xy, position.xy) * limits.xy * 2.0;

                    // 输出更新后的位置
                    gl_FragColor = position;
                }
            `;

            // 速度更新片段着色器 - GPGPU计算，用于更新粒子速度
            const velocityFragment = /* glsl */ `
                precision highp float;

                uniform float uTime;            // 时间uniform（未在此着色器中使用）
                uniform sampler2D tPosition;    // 位置纹理
                uniform sampler2D tMap;         // 当前速度纹理（tMap是GPGPU的默认输入）
                uniform vec2 uMouse;            // 鼠标位置（-1到1范围）

                varying vec2 vUv;               // 纹理坐标

                void main() {
                    // 从纹理中读取当前位置和速度
                    vec4 position = texture2D(tPosition, vUv);
                    vec4 velocity = texture2D(tMap, vUv);

                    // 鼠标排斥力效果
                    vec2 toMouse = position.xy - uMouse;        // 从鼠标到粒子的向量
                    float strength = smoothstep(0.3, 0.0, length(toMouse)); // 距离越近，力越强
                    velocity.xy += strength * normalize(toMouse) * 0.5;      // 添加排斥力

                    // 摩擦力：逐渐减慢粒子速度，防止无限加速
                    velocity.xy *= 0.98;

                    // 输出更新后的速度
                    gl_FragColor = velocity;
                }
            `;

            {
                // === 初始化渲染器和基础设置 ===
                const renderer = new Renderer({ dpr: 2 }); // 创建渲染器，设备像素比为2
                const gl = renderer.gl; // 获取WebGL上下文
                document.body.appendChild(gl.canvas); // 将画布添加到页面
                gl.clearColor(1, 1, 1, 1); // 设置清除颜色为白色

                // 创建相机
                const camera = new Camera(gl, { fov: 45 });
                camera.position.set(0, 0, 5); // 设置相机位置

                // 窗口大小调整处理函数
                function resize() {
                    renderer.setSize(window.innerWidth, window.innerHeight); // 调整渲染器大小
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height }); // 更新相机宽高比
                }
                window.addEventListener('resize', resize, false);
                resize(); // 初始调用

                // === 通用uniform变量 ===
                const time = { value: 0 }; // 时间uniform
                const mouse = { value: new Vec2() }; // 鼠标位置uniform

                // === GPGPU纹理大小和粒子数量配置 ===
                // 粒子数量决定了GPGPU纹理的大小，进而影响GPU计算的开销
                // 这里使用65536个粒子，正好填满256x256的纹理（256*256=65536）
                // 如果使用65537个粒子，就需要512x512的纹理，但会浪费大量像素
                // （512*512-65537=196607个像素将被浪费，约占3/4）
                const numParticles = 65536;

                // === 创建初始数据数组 ===
                // 为位置和速度创建初始数据数组，每个粒子4个值（RGBA通道）
                const initialPositionData = new Float32Array(numParticles * 4);
                const initialVelocityData = new Float32Array(numParticles * 4);

                // 随机值数组，用作常规静态顶点属性（不通过GPGPU更新）
                const random = new Float32Array(numParticles * 4);

                // 初始化每个粒子的数据
                for (let i = 0; i < numParticles; i++) {
                    // 设置初始位置：在-1到1范围内随机分布
                    initialPositionData.set(
                        [
                            (Math.random() - 0.5) * 2, // X坐标：-1到1
                            (Math.random() - 0.5) * 2, // Y坐标：-1到1
                            0, // Z坐标：在此示例中未使用绿色通道
                            1, // Alpha通道：设为1以便在WebGL调试器中可见
                        ],
                        i * 4
                    );
                    // 设置初始速度：所有粒子初始静止
                    initialVelocityData.set([0, 0, 0, 1], i * 4);
                    // 设置随机值：用于粒子的视觉变化和动画
                    random.set([Math.random(), Math.random(), Math.random(), Math.random()], i * 4);
                }

                // === 初始化GPGPU类 ===
                // 创建GPGPU实例，自动生成FBO（帧缓冲对象）和对应的纹理坐标
                const position = new GPGPU(gl, { data: initialPositionData }); // 位置计算GPGPU
                const velocity = new GPGPU(gl, { data: initialVelocityData }); // 速度计算GPGPU

                // === 添加模拟着色器通道 ===
                // 为每个GPGPU类添加计算通道（着色器程序）

                // 位置更新通道
                position.addPass({
                    fragment: positionFragment, // 使用位置更新着色器
                    uniforms: {
                        uTime: time, // 时间uniform
                        tVelocity: velocity.uniform, // 速度纹理作为输入
                    },
                });

                // 速度更新通道
                velocity.addPass({
                    fragment: velocityFragment, // 使用速度更新着色器
                    uniforms: {
                        uTime: time, // 时间uniform
                        uMouse: mouse, // 鼠标位置uniform
                        tPosition: position.uniform, // 位置纹理作为输入
                    },
                });

                // === 创建渲染几何体 ===
                // 现在可以创建几何体，使用上面生成的坐标
                // 注意：我们不使用速度或位置数据作为顶点属性
                // 而是在着色器中从FBO纹理中获取这些数据
                const geometry = new Geometry(gl, {
                    random: { size: 4, data: random }, // 随机值作为静态顶点属性

                    // 纹理坐标：用于从GPGPU纹理中采样数据
                    // 可以使用position或velocity的coords，因为它们是相同的
                    coords: { size: 2, data: position.coords },
                });

                // === 创建渲染着色器程序 ===
                const program = new Program(gl, {
                    vertex, // 粒子渲染顶点着色器
                    fragment, // 粒子渲染片段着色器
                    uniforms: {
                        uTime: time, // 时间uniform
                        tPosition: position.uniform, // 位置纹理
                        tVelocity: velocity.uniform, // 速度纹理
                    },
                });

                // === 创建粒子网格对象 ===
                // 使用POINTS模式渲染，每个顶点渲染为一个点精灵
                const points = new Mesh(gl, { geometry, program, mode: gl.POINTS });

                // === 鼠标/触摸交互处理 ===
                // 检测设备是否支持触摸并设置相应的事件监听器
                const isTouchCapable = 'ontouchstart' in window;
                if (isTouchCapable) {
                    // 触摸设备事件
                    window.addEventListener('touchstart', updateMouse, false);
                    window.addEventListener('touchmove', updateMouse, false);
                } else {
                    // 鼠标设备事件
                    window.addEventListener('mousemove', updateMouse, false);
                }

                // 鼠标/触摸位置更新函数
                function updateMouse(e) {
                    // 处理触摸事件的坐标获取
                    if (e.changedTouches && e.changedTouches.length) {
                        e.x = e.changedTouches[0].pageX;
                        e.y = e.changedTouches[0].pageY;
                    }
                    // 处理鼠标事件的坐标获取
                    if (e.x === undefined) {
                        e.x = e.pageX;
                        e.y = e.pageY;
                    }

                    // 将鼠标坐标转换为-1到1范围，y轴翻转以匹配WebGL坐标系
                    mouse.value.set((e.x / gl.renderer.width) * 2 - 1, (1 - e.y / gl.renderer.height) * 2 - 1);
                }

                // === 主渲染循环 ===
                requestAnimationFrame(update);
                function update(t) {
                    requestAnimationFrame(update);

                    // 更新时间uniform（转换为秒）
                    time.value = t * 0.001;

                    // === 执行GPGPU计算 ===
                    // 注意：顺序很重要！
                    // 1. 首先更新速度（基于当前位置和鼠标位置）
                    velocity.render();
                    // 2. 然后更新位置（基于新计算的速度）
                    position.render();

                    // === 渲染粒子 ===
                    // 使用更新后的位置和速度数据渲染所有粒子
                    renderer.render({ scene: points, camera });
                }
            }
        </script>
    </body>
</html>
