<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Sort Transparency</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info">Sort Transparency</div>
        <script type="module">
            // ========== 导入OGL核心模块 ==========
            import { Renderer, Camera, Transform, Texture, Program, Mesh, Color, Plane } from '../src/index.js';

            // ========== 顶点着色器 ==========
            // 🔬 **功能说明**：
            // 创建具有弯曲效果的平面几何体，模拟叶子的自然形状。
            // 通过UV坐标计算距离中心的偏移，产生凹凸效果。
            const vertex = /* glsl */ `
                // ========== 顶点属性 ==========
                attribute vec2 uv;       // 纹理坐标
                attribute vec3 position; // 顶点位置

                // ========== 变换矩阵 ==========
                uniform mat4 modelViewMatrix;  // 模型视图矩阵
                uniform mat4 projectionMatrix; // 投影矩阵

                // ========== 传递给片段着色器的变量 ==========
                varying vec2 vUv;    // 纹理坐标
                varying vec4 vMVPos; // 模型视图空间位置（用于雾效和颜色变化）

                void main() {
                    vUv = uv;
                    vec3 pos = position;

                    // ========== 创建弯曲效果 ==========
                    // 🔬 **弯曲算法**：
                    // 1. 计算UV坐标到中心点(0.5, 0.5)的距离
                    // 2. 使用平方函数增强边缘效果
                    // 3. 减去0.25创建凹陷效果（中心高，边缘低）
                    // 4. 将偏移应用到Z轴，创建3D弯曲
                    float dist = pow(length(vUv - 0.5), 2.0) - 0.25;
                    pos.z += dist * 0.5; // 沿Z轴偏移，创建弯曲的叶子形状

                    // ========== 位置变换 ==========
                    vMVPos = modelViewMatrix * vec4(pos, 1.0);      // 变换到视图空间
                    gl_Position = projectionMatrix * vMVPos;        // 变换到裁剪空间
                }
            `;

            // ========== 片段着色器 ==========
            // 🔬 **功能说明**：
            // 实现透明叶子材质，包含纹理采样、颜色变化、雾效和透明度处理。
            // 关键特性：基于纹理的透明度、距离雾效、位置相关的颜色变化。
            const fragment = /* glsl */ `
                precision highp float;

                // ========== 统一变量 ==========
                uniform sampler2D tMap; // 叶子纹理（使用绿色通道作为透明度）
                uniform vec3 uColor;    // 基础颜色

                // ========== 从顶点着色器传入的变量 ==========
                varying vec2 vUv;    // 纹理坐标
                varying vec4 vMVPos; // 模型视图空间位置

                void main() {
                    // ========== 透明度采样 ==========
                    // 🔬 **透明度处理**：
                    // 使用纹理的绿色通道作为透明度值。
                    // 这种技术常用于叶子、草地等自然物体的透明效果。
                    float alpha = texture2D(tMap, vUv).g;

                    // ========== 颜色计算 ==========
                    // 🎨 **动态颜色**：
                    // 基础颜色 + 基于位置的颜色变化，创建更自然的视觉效果。
                    // vMVPos.xzy 重新排列坐标轴，创建有趣的颜色渐变。
                    vec3 color = uColor + vMVPos.xzy * 0.05;

                    // ========== 距离雾效 ==========
                    // 🌫️ **雾效算法**：
                    // 1. 计算片段到相机的距离
                    // 2. 使用smoothstep在5.0到10.0单位间创建平滑过渡
                    // 3. 将颜色与白色混合，模拟大气散射效果
                    float dist = length(vMVPos);                    // 计算距离
                    float fog = smoothstep(5.0, 10.0, dist);       // 雾效强度
                    color = mix(color, vec3(1.0), fog);             // 混合雾色（白色）

                    // ========== 最终颜色输出 ==========
                    gl_FragColor.rgb = color;
                    gl_FragColor.a = alpha;

                    // ========== 透明度剔除 ==========
                    // 🔬 **性能优化**：
                    // 丢弃几乎完全透明的片段，避免不必要的混合计算。
                    // 这对透明度排序的性能很重要。
                    if (alpha < 0.01) discard;
                }
            `;

            // ========== 透明度排序演示 ==========
            // 🔬 **核心概念**：
            // 演示WebGL中透明对象的深度排序机制。透明对象必须从后往前渲染，
            // 以确保正确的混合效果。OGL框架自动处理几何体级别的排序。
            //
            // ⚠️ **重要说明**：
            // 此示例展示几何体间的排序，不包括单个几何体内部面/点的排序。
            // 对于复杂的透明几何体，可能需要额外的面级排序。

            {
                // ========== 渲染器设置 ==========
                const renderer = new Renderer({ dpr: 2 }); // 高DPI支持，提升显示质量
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);
                gl.clearColor(1, 1, 1, 1); // 白色背景，便于观察透明效果

                // ========== 相机设置 ==========
                const camera = new Camera(gl, { fov: 35 }); // 35度视野角，适中的透视效果
                camera.position.set(0, 0, 7); // 相机位置：稍微远离场景
                camera.rotation.z = -0.3; // 轻微倾斜，增加动态感

                // ========== 响应式处理 ==========
                function resize() {
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize();

                // ========== 场景根节点 ==========
                const scene = new Transform(); // 场景变换节点，所有对象的父节点

                // ========== 几何体创建 ==========
                // 🔬 **平面几何体**：
                // 使用分段平面创建更平滑的弯曲效果。
                // 更多分段 = 更平滑的变形，但性能开销更大。
                const geometry = new Plane(gl, {
                    widthSegments: 10, // 宽度方向10个分段
                    heightSegments: 10, // 高度方向10个分段
                });

                // ========== 纹理加载 ==========
                // 🖼️ **叶子纹理**：
                // 使用叶子图片的绿色通道作为透明度遮罩。
                // 这是实现自然透明效果的常用技术。
                const texture = new Texture(gl);
                const img = new Image();
                img.onload = () => (texture.image = img); // 异步加载纹理
                img.src = 'assets/leaf.jpg';

                // ========== 材质程序设置 ==========
                const program = new Program(gl, {
                    vertex, // 使用上面定义的顶点着色器
                    fragment, // 使用上面定义的片段着色器
                    uniforms: {
                        tMap: { value: texture }, // 叶子纹理
                        uColor: { value: new Color('#ffc219') }, // 金黄色基础色调
                    },
                    transparent: true, // ⭐ 启用透明度混合
                    cullFace: false, // ⭐ 禁用背面剔除，双面渲染
                });

                // ========== 网格对象数组 ==========
                const meshes = [];

                // ========== 创建多个叶子实例 ==========
                // 🍃 **叶子群组**：
                // 创建50个叶子实例，每个都有随机的位置、旋转、缩放和速度。
                // 这些参数的随机化创造了自然的飘落效果。
                for (let i = 0; i < 50; i++) {
                    const mesh = new Mesh(gl, { geometry, program });

                    // ========== 随机位置 ==========
                    // X: -1.5 到 1.5，Z: -1.5 到 1.5（水平分布）
                    // Y: -3 到 3（垂直分布，模拟不同高度的叶子）
                    mesh.position.set(
                        (Math.random() - 0.5) * 3, // X轴随机位置
                        (Math.random() - 0.5) * 6, // Y轴随机位置
                        (Math.random() - 0.5) * 3 // Z轴随机位置
                    );

                    // ========== 随机旋转 ==========
                    // Y和Z轴随机旋转，创建自然的叶子朝向
                    mesh.rotation.set(
                        0, // X轴不旋转
                        (Math.random() - 0.5) * 6.28, // Y轴随机旋转（0-2π）
                        (Math.random() - 0.5) * 6.28 // Z轴随机旋转（0-2π）
                    );

                    // ========== 随机缩放 ==========
                    // 0.2到0.7的随机缩放，模拟不同大小的叶子
                    mesh.scale.set(Math.random() * 0.5 + 0.2);

                    // ========== 自定义属性 ==========
                    mesh.speed = Math.random() * 1.5 + 0.2; // 随机下落速度

                    // ========== 添加到场景 ==========
                    mesh.setParent(scene); // 设置父节点
                    meshes.push(mesh); // 添加到数组便于管理
                }

                // ========== 动画循环 ==========
                requestAnimationFrame(update);
                function update(t) {
                    requestAnimationFrame(update);

                    // ========== 叶子动画 ==========
                    // 🍃 **飘落动画**：
                    // 每个叶子都有旋转和下落动画，速度各不相同。
                    meshes.forEach((mesh) => {
                        mesh.rotation.y += 0.05; // Y轴旋转（摆动）
                        mesh.rotation.z += 0.05; // Z轴旋转（翻滚）

                        // ========== 下落运动 ==========
                        mesh.position.y -= 0.02 * mesh.speed; // 根据速度下落

                        // ========== 循环重置 ==========
                        // 当叶子落到底部时，重新从顶部开始
                        if (mesh.position.y < -3) mesh.position.y += 6;
                    });

                    // ========== 场景整体旋转 ==========
                    // 缓慢旋转整个场景，增加观察角度的变化
                    scene.rotation.y += 0.015;

                    // ========== 渲染场景 ==========
                    // 🔬 **自动排序**：
                    // OGL渲染器会自动对透明对象进行深度排序。
                    // 确保透明对象从后往前渲染，实现正确的混合效果。
                    //
                    // 📐 **排序原理**：
                    // 1. 计算每个对象到相机的距离
                    // 2. 按距离从远到近排序
                    // 3. 先渲染不透明对象，再渲染透明对象
                    // 4. 透明对象按从后往前的顺序渲染
                    renderer.render({ scene, camera });
                }
            }
        </script>
    </body>
</html>
