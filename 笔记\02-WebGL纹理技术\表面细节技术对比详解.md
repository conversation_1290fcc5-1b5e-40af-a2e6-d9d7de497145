# 表面细节技术对比详解

## 概述

在3D渲染中，有多种技术可以为平面几何体添加表面细节，从简单的凹凸贴图到复杂的视差贴图。这些技术的核心区别在于：

- **凹凸贴图 (Bump Mapping)**：通过扰动法线影响光照计算
- **法线贴图 (Normal Mapping)**：直接存储法线信息，更精确的光照
- **位移贴图 (Displacement Mapping)**：真实改变几何体顶点位置
- **视差贴图 (Parallax Mapping)**：模拟深度视差效果，最逼真的视觉效果

## 1. 凹凸贴图 (Bump Mapping)

### 1.1 基本原理
凹凸贴图使用高度图来扰动表面法线，创造凹凸感。它**不需要光照就能工作**，但有光照效果更明显。

### 1.2 Shader实现

```glsl
// 顶点着色器
attribute vec3 position;
attribute vec3 normal;
attribute vec2 uv;

uniform mat4 modelViewMatrix;
uniform mat4 projectionMatrix;
uniform mat3 normalMatrix;

varying vec3 vNormal;
varying vec2 vUv;
varying vec3 vPosition;

void main() {
    vNormal = normalize(normalMatrix * normal);
    vUv = uv;
    vPosition = (modelViewMatrix * vec4(position, 1.0)).xyz;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
}
```

```glsl
// 片元着色器 - 凹凸贴图
precision mediump float;

uniform sampler2D uHeightMap;    // 高度图
uniform float uBumpScale;       // 凹凸强度
uniform vec3 uLightPos;         // 光源位置

varying vec3 vNormal;
varying vec2 vUv;
varying vec3 vPosition;

vec3 calculateBumpNormal() {
    // 采样当前点和相邻点的高度
    float height = texture2D(uHeightMap, vUv).r;
    float heightU = texture2D(uHeightMap, vUv + vec2(0.001, 0.0)).r;
    float heightV = texture2D(uHeightMap, vUv + vec2(0.0, 0.001)).r;
    
    // 计算高度梯度
    vec2 gradient = vec2(heightU - height, heightV - height) * uBumpScale;
    
    // 扰动原始法线
    vec3 normal = normalize(vNormal);
    vec3 tangent = normalize(cross(normal, vec3(0.0, 1.0, 0.0)));
    vec3 bitangent = cross(normal, tangent);
    
    // 应用扰动
    return normalize(normal + gradient.x * tangent + gradient.y * bitangent);
}

void main() {
    vec3 bumpNormal = calculateBumpNormal();
    
    // 简单光照计算
    vec3 lightDir = normalize(uLightPos - vPosition);
    float lighting = max(dot(bumpNormal, lightDir), 0.0);
    
    // 基础颜色 + 光照
    vec3 baseColor = vec3(0.8, 0.6, 0.4);
    gl_FragColor = vec4(baseColor * lighting, 1.0);
}
```

### 1.3 特点
- ✅ 简单实现，性能好
- ✅ 不需要额外的切线空间计算
- ❌ 效果相对粗糙
- ❌ 在边缘处可能出现不连续

## 2. 法线贴图 (Normal Mapping)

### 2.1 基本原理
法线贴图直接存储表面的法线信息，通过**扰动反射光**来创造凹凸感。**必须有光照才能看到效果**。

### 2.2 Shader实现

```glsl
// 顶点着色器 - 需要切线空间
attribute vec3 position;
attribute vec3 normal;
attribute vec3 tangent;
attribute vec2 uv;

uniform mat4 modelViewMatrix;
uniform mat4 projectionMatrix;
uniform mat3 normalMatrix;

varying vec3 vNormal;
varying vec3 vTangent;
varying vec3 vBitangent;
varying vec2 vUv;
varying vec3 vPosition;

void main() {
    vNormal = normalize(normalMatrix * normal);
    vTangent = normalize(normalMatrix * tangent);
    vBitangent = cross(vNormal, vTangent);
    vUv = uv;
    vPosition = (modelViewMatrix * vec4(position, 1.0)).xyz;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
}
```

```glsl
// 片元着色器 - 法线贴图
precision mediump float;

uniform sampler2D uNormalMap;    // 法线贴图
uniform sampler2D uDiffuseMap;   // 漫反射贴图
uniform float uNormalScale;     // 法线强度
uniform vec3 uLightPos;         // 光源位置

varying vec3 vNormal;
varying vec3 vTangent;
varying vec3 vBitangent;
varying vec2 vUv;
varying vec3 vPosition;

vec3 getNormalFromMap() {
    // 从法线贴图采样 (存储在切线空间)
    vec3 tangentNormal = texture2D(uNormalMap, vUv).xyz * 2.0 - 1.0;
    tangentNormal.xy *= uNormalScale;
    
    // 构建TBN矩阵 (切线空间到世界空间)
    mat3 TBN = mat3(
        normalize(vTangent),
        normalize(vBitangent),
        normalize(vNormal)
    );
    
    // 转换到世界空间
    return normalize(TBN * tangentNormal);
}

void main() {
    vec3 normal = getNormalFromMap();
    vec3 diffuseColor = texture2D(uDiffuseMap, vUv).rgb;
    
    // Phong光照模型
    vec3 lightDir = normalize(uLightPos - vPosition);
    vec3 viewDir = normalize(-vPosition);
    vec3 reflectDir = reflect(-lightDir, normal);
    
    // 环境光
    vec3 ambient = 0.1 * diffuseColor;
    
    // 漫反射
    float diff = max(dot(normal, lightDir), 0.0);
    vec3 diffuse = diff * diffuseColor;
    
    // 镜面反射
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32.0);
    vec3 specular = spec * vec3(1.0);
    
    gl_FragColor = vec4(ambient + diffuse + specular, 1.0);
}
```

### 2.3 特点
- ✅ 效果精确，细节丰富
- ✅ 标准的工业实践
- ✅ 支持复杂的光照模型
- ❌ 需要切线空间计算
- ❌ 必须有光照才能看到效果

## 3. 位移贴图 (Displacement Mapping)

### 3.1 基本原理
位移贴图真实地改变几何体的顶点位置，创造真正的几何细节。

### 3.2 Shader实现

```glsl
// 顶点着色器 - 位移贴图
attribute vec3 position;
attribute vec3 normal;
attribute vec2 uv;

uniform mat4 modelViewMatrix;
uniform mat4 projectionMatrix;
uniform mat3 normalMatrix;
uniform sampler2D uDisplacementMap;  // 位移贴图
uniform float uDisplacementScale;   // 位移强度

varying vec3 vNormal;
varying vec2 vUv;
varying vec3 vPosition;

void main() {
    // 采样位移值
    float displacement = texture2D(uDisplacementMap, uv).r;
    
    // 沿法线方向位移顶点
    vec3 displacedPosition = position + normal * displacement * uDisplacementScale;
    
    vNormal = normalize(normalMatrix * normal);
    vUv = uv;
    vPosition = (modelViewMatrix * vec4(displacedPosition, 1.0)).xyz;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(displacedPosition, 1.0);
}
```

```glsl
// 片元着色器 - 位移贴图
precision mediump float;

uniform sampler2D uDiffuseMap;
uniform vec3 uLightPos;

varying vec3 vNormal;
varying vec2 vUv;
varying vec3 vPosition;

void main() {
    vec3 normal = normalize(vNormal);
    vec3 diffuseColor = texture2D(uDiffuseMap, vUv).rgb;
    
    // 简单光照
    vec3 lightDir = normalize(uLightPos - vPosition);
    float lighting = max(dot(normal, lightDir), 0.0);
    
    gl_FragColor = vec4(diffuseColor * lighting, 1.0);
}
```

### 3.3 特点
- ✅ 真实的几何细节
- ✅ 正确的轮廓和阴影
- ❌ 需要高密度网格
- ❌ 性能开销大
- ❌ 需要细分曲面支持

## 4. 视差贴图 (Parallax Mapping)

### 4.1 基本原理
视差贴图通过模拟深度视差效果，在不改变几何体的情况下创造最逼真的深度感。

### 4.2 基础视差贴图

```glsl
// 片元着色器 - 基础视差贴图
precision mediump float;

uniform sampler2D uDiffuseMap;
uniform sampler2D uHeightMap;
uniform float uHeightScale;
uniform vec3 uLightPos;
uniform vec3 uViewPos;

varying vec3 vNormal;
varying vec3 vTangent;
varying vec3 vBitangent;
varying vec2 vUv;
varying vec3 vPosition;

vec2 parallaxMapping(vec2 texCoords, vec3 viewDir) {
    // 采样高度
    float height = texture2D(uHeightMap, texCoords).r;
    
    // 计算视差偏移
    vec2 p = viewDir.xy / viewDir.z * (height * uHeightScale);
    
    // 返回偏移后的纹理坐标
    return texCoords - p;
}

void main() {
    // 构建TBN矩阵
    mat3 TBN = mat3(
        normalize(vTangent),
        normalize(vBitangent),
        normalize(vNormal)
    );
    
    // 计算切线空间的观察方向
    vec3 viewDir = normalize(uViewPos - vPosition);
    vec3 tangentViewDir = normalize(transpose(TBN) * viewDir);
    
    // 应用视差映射
    vec2 parallaxUV = parallaxMapping(vUv, tangentViewDir);
    
    // 边界检查
    if (parallaxUV.x > 1.0 || parallaxUV.y > 1.0 || parallaxUV.x < 0.0 || parallaxUV.y < 0.0) {
        discard;
    }
    
    // 使用偏移后的UV采样纹理
    vec3 diffuseColor = texture2D(uDiffuseMap, parallaxUV).rgb;
    
    // 光照计算
    vec3 lightDir = normalize(uLightPos - vPosition);
    float lighting = max(dot(normalize(vNormal), lightDir), 0.0);
    
    gl_FragColor = vec4(diffuseColor * lighting, 1.0);
}
```

### 4.3 陡峭视差贴图 (Steep Parallax Mapping)

```glsl
vec2 steepParallaxMapping(vec2 texCoords, vec3 viewDir) {
    // 分层数量
    const float numLayers = 32.0;
    
    // 每层的深度
    float layerDepth = 1.0 / numLayers;
    float currentLayerDepth = 0.0;
    
    // 每层的UV偏移量
    vec2 P = viewDir.xy * uHeightScale;
    vec2 deltaTexCoords = P / numLayers;
    
    vec2 currentTexCoords = texCoords;
    float currentDepthMapValue = texture2D(uHeightMap, currentTexCoords).r;
    
    // 逐层搜索交点
    while (currentLayerDepth < currentDepthMapValue) {
        currentTexCoords -= deltaTexCoords;
        currentDepthMapValue = texture2D(uHeightMap, currentTexCoords).r;
        currentLayerDepth += layerDepth;
    }
    
    return currentTexCoords;
}
```

### 4.4 视差遮蔽贴图 (Parallax Occlusion Mapping)

```glsl
vec2 parallaxOcclusionMapping(vec2 texCoords, vec3 viewDir) {
    const float numLayers = 32.0;
    float layerDepth = 1.0 / numLayers;
    float currentLayerDepth = 0.0;
    
    vec2 P = viewDir.xy * uHeightScale;
    vec2 deltaTexCoords = P / numLayers;
    
    vec2 currentTexCoords = texCoords;
    float currentDepthMapValue = texture2D(uHeightMap, currentTexCoords).r;
    
    // 找到交点前后的两层
    while (currentLayerDepth < currentDepthMapValue) {
        currentTexCoords -= deltaTexCoords;
        currentDepthMapValue = texture2D(uHeightMap, currentTexCoords).r;
        currentLayerDepth += layerDepth;
    }
    
    // 获取交点前的纹理坐标
    vec2 prevTexCoords = currentTexCoords + deltaTexCoords;
    
    // 获取交点前后的深度值
    float afterDepth = currentDepthMapValue - currentLayerDepth;
    float beforeDepth = texture2D(uHeightMap, prevTexCoords).r - currentLayerDepth + layerDepth;
    
    // 线性插值得到精确的交点
    float weight = afterDepth / (afterDepth - beforeDepth);
    vec2 finalTexCoords = prevTexCoords * weight + currentTexCoords * (1.0 - weight);
    
    return finalTexCoords;
}
```

### 4.5 特点
- ✅ 最逼真的深度效果
- ✅ 正确的视差和遮蔽
- ✅ 不需要额外几何体
- ❌ 计算复杂，性能开销大
- ❌ 在极端角度可能出现伪影

## 5. 技术对比总结

| 技术 | 几何改变 | 光照依赖 | 性能 | 视觉效果 | 适用场景 |
|------|----------|----------|------|----------|----------|
| 凹凸贴图 | ❌ | 可选 | 高 | 基础 | 简单表面细节 |
| 法线贴图 | ❌ | 必需 | 高 | 良好 | 标准细节渲染 |
| 位移贴图 | ✅ | 可选 | 低 | 最佳 | 高质量渲染 |
| 视差贴图 | ❌ | 可选 | 中 | 优秀 | 实时高质量效果 |

## 6. 实际应用建议

### 6.1 性能优先
```glsl
// 移动设备或低端硬件
#ifdef LOW_QUALITY
    // 使用简单凹凸贴图
    vec3 normal = calculateBumpNormal();
#else
    // 使用法线贴图
    vec3 normal = getNormalFromMap();
#endif
```

### 6.2 质量优先
```glsl
// 根据距离选择技术
float distance = length(uViewPos - vPosition);
if (distance < 10.0) {
    // 近距离使用视差遮蔽贴图
    vec2 uv = parallaxOcclusionMapping(vUv, tangentViewDir);
} else if (distance < 50.0) {
    // 中距离使用基础视差贴图
    vec2 uv = parallaxMapping(vUv, tangentViewDir);
} else {
    // 远距离使用法线贴图
    vec2 uv = vUv;
}
```

这些技术各有优势，选择哪种取决于具体的性能要求和视觉效果需求。在实际项目中，通常会根据硬件能力和距离进行动态选择。
