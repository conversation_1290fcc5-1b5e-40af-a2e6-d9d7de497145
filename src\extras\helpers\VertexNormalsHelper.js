// ========== 导入依赖模块 ==========
import { Mesh } from '../../core/Mesh.js'; // 网格基类
import { Program } from '../../core/Program.js'; // 着色器程序类
import { Geometry } from '../../core/Geometry.js'; // 几何体类
import { Vec3 } from '../../math/Vec3.js'; // 三维向量类
import { Mat3 } from '../../math/Mat3.js'; // 三维矩阵类

/**
 * 顶点法线辅助器类
 *
 * 🔬 **功能说明**：
 * 用于可视化3D模型的顶点法线向量，通过绘制从每个顶点出发的线段来显示法线方向。
 * 这对于调试光照、法线贴图、几何体法线等问题非常有用。
 *
 * 📐 **工作原理**：
 * 1. 从目标对象提取顶点位置和法线数据
 * 2. 为每个顶点创建一条线段（起点=顶点位置，终点=顶点位置+法线*长度）
 * 3. 使用GL_LINES模式渲染这些线段
 * 4. 在顶点着色器中根据size属性控制线段长度
 *
 * 💡 **使用场景**：
 * - 调试模型的法线方向是否正确
 * - 检查法线贴图的效果
 * - 验证几何体变换后法线的变化
 * - 学习和理解3D图形学中的法线概念
 */
export class VertexNormalsHelper extends Mesh {
    /**
     * 构造函数
     *
     * @param {Object} object - 要显示法线的目标对象（必须包含position和normal属性）
     * @param {Object} options - 配置选项
     * @param {number} options.size - 法线线段的长度（默认0.1）
     * @param {Vec3} options.color - 法线线段的颜色（默认洋红色）
     * @param {Object} meshProps - 传递给Mesh基类的其他属性
     */
    constructor(object, { size = 0.1, color = new Vec3(0.86, 0.16, 0.86), ...meshProps } = {}) {
        const gl = object.gl;

        // ========== 获取目标对象的几何数据 ==========
        const nNormals = object.geometry.attributes.normal.count; // 法线数量（等于顶点数量）

        // ========== 创建线段几何数据数组 ==========
        // 每个顶点需要2个点来形成一条线段（起点和终点）
        // 每个点需要3个坐标分量（x, y, z）
        const positionsArray = new Float32Array(nNormals * 2 * 3); // 位置数据：顶点数 × 2点 × 3坐标
        const normalsArray = new Float32Array(nNormals * 2 * 3); // 法线数据：顶点数 × 2点 × 3坐标
        const sizeArray = new Float32Array(nNormals * 2); // 尺寸数据：顶点数 × 2点

        // ========== 获取原始几何数据 ==========
        const normalData = object.geometry.attributes.normal.data; // 原始法线数据
        const positionData = object.geometry.attributes.position.data; // 原始位置数据
        const sizeData = new Float32Array([0, size]); // 线段的起点和终点的尺寸值

        // ========== 构建线段几何数据 ==========
        // 🔬 **数据结构说明**：
        // 对于每个顶点，我们创建一条线段：
        // - 起点：顶点位置，size=0（不偏移）
        // - 终点：顶点位置，size=法线长度（在着色器中沿法线方向偏移）
        for (let i = 0; i < nNormals; i++) {
            const i6 = i * 6; // 每个顶点在线段数组中占6个位置（2点 × 3坐标）
            const i3 = i * 3; // 每个顶点在原始数组中占3个位置（1点 × 3坐标）

            // ========== 复制位置数据 ==========
            // 为线段的起点和终点设置相同的位置（实际偏移在着色器中计算）
            const pSub = positionData.subarray(i3, i3 + 3); // 获取当前顶点的位置
            positionsArray.set(pSub, i6); // 设置线段起点位置
            positionsArray.set(pSub, i6 + 3); // 设置线段终点位置（相同）

            // ========== 复制法线数据 ==========
            // 为线段的起点和终点设置相同的法线方向
            const nSub = normalData.subarray(i3, i3 + 3); // 获取当前顶点的法线
            normalsArray.set(nSub, i6); // 设置线段起点法线
            normalsArray.set(nSub, i6 + 3); // 设置线段终点法线（相同）

            // ========== 设置尺寸数据 ==========
            // 起点size=0，终点size=指定长度
            // 着色器将根据这个值来偏移顶点位置
            sizeArray.set(sizeData, i * 2); // [0, size] 分别对应起点和终点
        }

        // ========== 创建线段几何体 ==========
        const geometry = new Geometry(gl, {
            position: { size: 3, data: positionsArray }, // 位置属性：3个分量(x,y,z)
            normal: { size: 3, data: normalsArray }, // 法线属性：3个分量(x,y,z)
            size: { size: 1, data: sizeArray }, // 尺寸属性：1个分量(length)
        });

        // ========== 创建着色器程序 ==========
        const program = new Program(gl, {
            vertex, // 顶点着色器（在文件末尾定义）
            fragment, // 片段着色器（在文件末尾定义）
            uniforms: {
                color: { value: color }, // 线段颜色
                worldNormalMatrix: { value: new Mat3() }, // 世界法线变换矩阵
                objectWorldMatrix: { value: object.worldMatrix }, // 对象世界变换矩阵
            },
        });

        // ========== 调用父类构造函数 ==========
        // 使用GL_LINES模式渲染线段
        super(gl, { ...meshProps, mode: gl.LINES, geometry, program });

        // ========== 保存目标对象引用 ==========
        this.object = object; // 用于在渲染时获取最新的变换矩阵
    }

    /**
     * 绘制方法
     *
     * 🔬 **关键操作**：
     * 在每次绘制前更新世界法线变换矩阵，确保法线方向正确。
     *
     * 📐 **法线变换原理**：
     * 当对象进行非均匀缩放时，法线不能简单地用模型矩阵变换，
     * 需要使用模型矩阵的逆转置矩阵来正确变换法线方向。
     *
     * @param {Object} arg - 渲染参数
     */
    draw(arg) {
        // ========== 更新世界法线变换矩阵 ==========
        // getNormalMatrix() 计算世界矩阵的逆转置矩阵，用于正确变换法线
        this.program.uniforms.worldNormalMatrix.value.getNormalMatrix(this.object.worldMatrix);

        // ========== 调用父类绘制方法 ==========
        super.draw(arg);
    }
}

// ========== 顶点着色器 ==========
// 🔬 **功能说明**：
// 负责将顶点位置变换到裁剪空间，并根据size属性沿法线方向偏移顶点位置。
// 这样可以创建从顶点位置出发、沿法线方向延伸的线段。
//
// 📐 **变换流程**：
// 1. 将法线从对象空间变换到世界空间
// 2. 将顶点位置从对象空间变换到世界空间
// 3. 根据size属性沿法线方向偏移世界位置
// 4. 将最终位置变换到裁剪空间
const vertex = /* glsl */ `
    // ========== 顶点属性 ==========
    attribute vec3 position;  // 顶点位置（对象空间）
    attribute vec3 normal;    // 顶点法线（对象空间）
    attribute float size;     // 偏移距离（0=起点，指定值=终点）

    // ========== 变换矩阵 ==========
    uniform mat4 viewMatrix;        // 视图矩阵（世界空间 → 视图空间）
    uniform mat4 projectionMatrix;  // 投影矩阵（视图空间 → 裁剪空间）
    uniform mat4 objectWorldMatrix; // 对象世界矩阵（对象空间 → 世界空间）
    uniform mat3 worldNormalMatrix; // 世界法线矩阵（对象法线 → 世界法线）

    void main() {
        // ========== 法线变换和缩放 ==========
        // 🔬 **关键操作**：
        // 1. 使用worldNormalMatrix将法线从对象空间变换到世界空间
        // 2. 归一化法线向量确保方向正确
        // 3. 乘以size控制法线线段的长度
        //
        // 📐 **为什么需要特殊的法线矩阵**：
        // 当对象进行非均匀缩放时，法线不能直接用模型矩阵变换，
        // 必须使用模型矩阵的逆转置矩阵来保持法线的垂直性。
        vec3 n = normalize(worldNormalMatrix * normal) * size;

        // ========== 位置变换 ==========
        // 将顶点位置从对象空间变换到世界空间
        vec3 p = (objectWorldMatrix * vec4(position, 1.0)).xyz;

        // ========== 最终位置计算 ==========
        // 🔬 **线段生成原理**：
        // - 当size=0时：p + n = p + 0 = p（线段起点）
        // - 当size=指定值时：p + n = p + 法线*长度（线段终点）
        // 这样每两个顶点就形成一条从顶点位置出发的法线线段
        gl_Position = projectionMatrix * viewMatrix * vec4(p + n, 1.0);
    }
`;

// ========== 片段着色器 ==========
// 🔬 **功能说明**：
// 非常简单的片段着色器，只是将所有像素渲染为统一的颜色。
// 不需要复杂的光照计算，因为这只是用于调试显示的辅助线段。
//
// 💡 **设计理念**：
// 保持简单和高效，确保法线线段在任何光照条件下都清晰可见。
const fragment = /* glsl */ `
    precision highp float;  // 使用高精度浮点数

    // ========== 统一变量 ==========
    uniform vec3 color;     // 线段颜色（RGB）

    void main() {
        // ========== 输出固定颜色 ==========
        // 所有法线线段都使用相同的颜色，便于识别和调试
        // Alpha通道设为1.0，确保线段完全不透明
        gl_FragColor = vec4(color, 1.0);
    }
`;
