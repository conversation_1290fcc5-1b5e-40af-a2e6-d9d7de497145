// 导入必要的核心组件
import { Program } from '../core/Program.js'; // 着色器程序类
import { Mesh } from '../core/Mesh.js'; // 网格对象类
import { Texture } from '../core/Texture.js'; // 纹理类
import { RenderTarget } from '../core/RenderTarget.js'; // 渲染目标（FBO）类
import { Triangle } from './Triangle.js'; // 三角形几何体类

/**
 * GPGPU (General-Purpose computing on Graphics Processing Units) 类
 *
 * 这个类允许在GPU上进行通用计算，而不仅仅是图形渲染。
 * 核心思想是将计算数据存储在纹理中，使用片段着色器进行并行计算，
 * 然后将结果写入到另一个纹理中。这种方法可以利用GPU的并行处理能力
 * 来加速大量数据的计算。
 *
 * 主要用途：
 * - 粒子系统模拟
 * - 物理计算
 * - 图像处理
 * - 数值计算
 */
export class GPGPU {
    /**
     * GPGPU构造函数
     *
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {Object} options - 配置选项
     * @param {Float32Array} options.data - 初始数据数组，必须是vec4数组（RGBA值）
     * @param {Geometry} options.geometry - 用于渲染的几何体，默认为三角形
     * @param {GLenum} options.type - 纹理数据类型，默认为HALF_FLOAT
     */
    constructor(
        gl,
        {
            // 总是传入vec4数组（纹理中的RGBA值）
            data = new Float32Array(16),
            geometry = new Triangle(gl),
            type, // 传入gl.FLOAT强制使用，默认为gl.HALF_FLOAT
        }
    ) {
        this.gl = gl;
        const initialData = data;
        this.passes = []; // 存储所有计算通道
        this.geometry = geometry; // 用于渲染的几何体
        this.dataLength = initialData.length / 4; // 数据元素数量（每个元素4个分量）

        // === 计算纹理大小 ===
        // Windows和iOS只支持2的幂次方纹理
        // 找到能容纳数据的最小2的幂次方大小
        this.size = Math.pow(2, Math.ceil(Math.log(Math.ceil(Math.sqrt(this.dataLength))) / Math.LN2));

        // === 创建纹理坐标 ===
        // 为输出纹理创建坐标数组，用于在着色器中采样正确的数据
        this.coords = new Float32Array(this.dataLength * 2);
        for (let i = 0; i < this.dataLength; i++) {
            // 计算每个数据元素在纹理中的UV坐标
            const x = (i % this.size) / this.size; // X坐标：列位置
            const y = Math.floor(i / this.size) / this.size; // Y坐标：行位置
            this.coords.set([x, y], i * 2);
        }

        // === 准备纹理数据 ===
        // 如果原始数据已经是正确的2的幂次方纹理长度，则直接使用
        // 否则复制到正确长度的新数组中
        const floatArray = (() => {
            if (initialData.length === this.size * this.size * 4) {
                return initialData;
            } else {
                const a = new Float32Array(this.size * this.size * 4);
                a.set(initialData); // 复制原始数据，剩余部分自动填充为0
                return a;
            }
        })();

        // === 创建输出纹理uniform ===
        // 使用初始数据创建浮点纹理，这个纹理将作为计算结果的输出
        this.uniform = {
            value: new Texture(gl, {
                image: floatArray, // 纹理数据
                target: gl.TEXTURE_2D, // 2D纹理
                type: gl.FLOAT, // 浮点数据类型
                format: gl.RGBA, // RGBA格式
                internalFormat: gl.renderer.isWebgl2 ? gl.RGBA32F : gl.RGBA, // 内部格式
                wrapS: gl.CLAMP_TO_EDGE, // S轴包装模式：边缘夹紧
                wrapT: gl.CLAMP_TO_EDGE, // T轴包装模式：边缘夹紧
                generateMipmaps: false, // 不生成mipmap
                minFilter: gl.NEAREST, // 最近邻过滤（精确采样）
                magFilter: gl.NEAREST, // 最近邻过滤（精确采样）
                width: this.size, // 纹理宽度
                flipY: false, // 不翻转Y轴
            }),
        };

        // === 创建帧缓冲对象（FBO）===
        // 配置FBO选项，用于双缓冲渲染
        const options = {
            width: this.size, // FBO宽度
            height: this.size, // FBO高度
            // 数据类型：优先使用传入的type，否则使用HALF_FLOAT
            type: type || gl.HALF_FLOAT || gl.renderer.extensions['OES_texture_half_float'].HALF_FLOAT_OES,
            format: gl.RGBA, // 颜色格式
            // 内部格式：根据WebGL版本和数据类型选择
            internalFormat: gl.renderer.isWebgl2 ? (type === gl.FLOAT ? gl.RGBA32F : gl.RGBA16F) : gl.RGBA,
            minFilter: gl.NEAREST, // 最近邻过滤（精确采样）
            depth: false, // 不需要深度缓冲
            unpackAlignment: 1, // 像素对齐方式
        };

        // === 创建双缓冲FBO系统 ===
        // 使用乒乓缓冲技术：一个用于读取，一个用于写入
        this.fbo = {
            read: new RenderTarget(gl, options), // 读取缓冲区
            write: new RenderTarget(gl, options), // 写入缓冲区

            // 交换读写缓冲区的函数
            swap: () => {
                let temp = this.fbo.read;
                this.fbo.read = this.fbo.write;
                this.fbo.write = temp;
                // 更新uniform纹理引用到新的读取缓冲区
                this.uniform.value = this.fbo.read.texture;
            },
        };
    }

    /**
     * 添加计算通道
     *
     * 每个通道代表一个计算步骤，使用片段着色器在GPU上并行处理数据
     *
     * @param {Object} options - 通道配置选项
     * @param {string} options.vertex - 顶点着色器代码，默认使用内置的全屏三角形着色器
     * @param {string} options.fragment - 片段着色器代码，包含实际的计算逻辑
     * @param {Object} options.uniforms - 着色器uniform变量
     * @param {string} options.textureUniform - 输入纹理的uniform名称，默认为'tMap'
     * @param {boolean} options.enabled - 是否启用此通道，默认为true
     * @returns {Object} 返回创建的通道对象
     */
    addPass({ vertex = defaultVertex, fragment = defaultFragment, uniforms = {}, textureUniform = 'tMap', enabled = true } = {}) {
        // 将当前GPGPU的输出纹理作为输入纹理添加到uniforms中
        uniforms[textureUniform] = this.uniform;

        // 创建着色器程序
        const program = new Program(this.gl, { vertex, fragment, uniforms });

        // 创建用于渲染的网格对象
        const mesh = new Mesh(this.gl, { geometry: this.geometry, program });

        // 创建通道对象
        const pass = {
            mesh, // 渲染网格
            program, // 着色器程序
            uniforms, // uniform变量
            enabled, // 是否启用
            textureUniform, // 输入纹理uniform名称
        };

        // 添加到通道列表
        this.passes.push(pass);
        return pass;
    }

    /**
     * 执行GPGPU计算
     *
     * 依次执行所有启用的计算通道，每个通道的输出成为下一个通道的输入
     * 使用双缓冲技术确保数据的正确传递
     */
    render() {
        // 过滤出所有启用的通道
        const enabledPasses = this.passes.filter((pass) => pass.enabled);

        // 依次执行每个通道
        enabledPasses.forEach((pass) => {
            // 渲染到写入缓冲区
            this.gl.renderer.render({
                scene: pass.mesh, // 使用通道的网格对象
                target: this.fbo.write, // 渲染到写入FBO
                clear: false, // 不清除缓冲区（保留之前的计算结果）
            });

            // 交换读写缓冲区，使当前的计算结果成为下一次计算的输入
            this.fbo.swap();
        });
    }
}

// === 默认着色器 ===

/**
 * 默认顶点着色器
 *
 * 创建一个覆盖整个屏幕的全屏四边形，用于GPGPU计算
 * 每个像素对应纹理中的一个数据元素
 */
const defaultVertex = /* glsl */ `
    attribute vec2 uv;          // 纹理坐标属性
    attribute vec2 position;    // 顶点位置属性

    varying vec2 vUv;           // 传递给片段着色器的纹理坐标

    void main() {
        vUv = uv;               // 传递纹理坐标
        gl_Position = vec4(position, 0, 1); // 设置顶点位置
    }
`;

/**
 * 默认片段着色器
 *
 * 简单的直通着色器，将输入纹理直接输出
 * 用于测试或作为计算通道的基础模板
 */
const defaultFragment = /* glsl */ `
    precision highp float;      // 使用高精度浮点数

    uniform sampler2D tMap;     // 输入纹理
    varying vec2 vUv;           // 从顶点着色器传入的纹理坐标

    void main() {
        // 从输入纹理采样并直接输出
        gl_FragColor = texture2D(tMap, vUv);
    }
`;
