<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebGL深度打包可视化演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
        }
        
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: #f7fafc;
        }
        
        .input-group {
            margin: 20px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2d3748;
        }
        
        input[type="range"] {
            width: 100%;
            margin: 10px 0;
        }
        
        .value-display {
            font-family: 'Courier New', monospace;
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            white-space: pre-line;
        }
        
        .color-channels {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        
        .channel {
            flex: 1;
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
        }
        
        .channel-r { background: #e53e3e; }
        .channel-g { background: #38a169; }
        .channel-b { background: #3182ce; }
        .channel-a { background: #805ad5; }
        
        .precision-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison {
            padding: 15px;
            border-radius: 8px;
            background: #edf2f7;
        }
        
        .error-display {
            background: #fed7d7;
            color: #c53030;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success-display {
            background: #c6f6d5;
            color: #2f855a;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 WebGL深度打包可视化演示</h1>
        
        <div class="demo-section">
            <h2>📊 深度值输入</h2>
            <div class="input-group">
                <label for="depthSlider">深度值 (0.0 - 1.0):</label>
                <input type="range" id="depthSlider" min="0" max="1" step="0.000001" value="0.123456">
                <div id="depthValue" class="value-display">深度值: 0.123456</div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔢 打包过程演示</h2>
            <div id="packingSteps" class="value-display">
                <!-- 动态生成内容 -->
            </div>
        </div>

        <div class="demo-section">
            <h2>🎨 RGBA通道可视化</h2>
            <div class="color-channels">
                <div class="channel channel-r">
                    <div>R通道</div>
                    <div id="channelR">0.123</div>
                    <div>×1.0</div>
                </div>
                <div class="channel channel-g">
                    <div>G通道</div>
                    <div id="channelG">0.456</div>
                    <div>×1/255</div>
                </div>
                <div class="channel channel-b">
                    <div>B通道</div>
                    <div id="channelB">0.789</div>
                    <div>×1/65025</div>
                </div>
                <div class="channel channel-a">
                    <div>A通道</div>
                    <div id="channelA">0.012</div>
                    <div>×1/16581375</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔄 解包验证</h2>
            <div class="precision-demo">
                <div class="comparison">
                    <h3>原始深度值</h3>
                    <div id="originalDepth" class="value-display">0.123456</div>
                </div>
                <div class="comparison">
                    <h3>解包后深度值</h3>
                    <div id="unpackedDepth" class="value-display">0.123456</div>
                </div>
            </div>
            <div id="errorAnalysis" class="error-display">
                精度误差: 0.000000001
            </div>
        </div>

        <div class="demo-section">
            <h2>💡 原理解释</h2>
            <div class="value-display">
为什么需要深度打包？

1. 兼容性问题：某些设备不支持浮点深度纹理
2. 精度需求：需要更高的数值精度存储深度信息
3. 数据传输：将深度作为颜色数据在着色器间传递

核心思想：
将一个高精度浮点数分解到4个8位通道中，
每个通道负责不同的精度级别，
就像把一个长数字分段存储在不同的"抽屉"里！
            </div>
        </div>
    </div>

    <script>
        // 深度打包函数（JavaScript版本）
        function packRGBA(depth) {
            const multipliers = [1.0, 255.0, 65025.0, 16581375.0];
            
            // 步骤1：乘以倍数并取小数部分
            let pack = multipliers.map(m => {
                const multiplied = m * depth;
                return multiplied - Math.floor(multiplied);
            });
            
            // 步骤2：防止精度溢出
            for (let i = 0; i < 3; i++) {
                pack[i] -= pack[i + 1] / 255.0;
            }
            
            return pack;
        }
        
        // 深度解包函数
        function unpackRGBA(rgba) {
            const weights = [1.0, 1/255.0, 1/65025.0, 1/16581375.0];
            return rgba.reduce((sum, val, i) => sum + val * weights[i], 0);
        }
        
        // 更新演示
        function updateDemo() {
            const depth = parseFloat(document.getElementById('depthSlider').value);
            const packed = packRGBA(depth);
            const unpacked = unpackRGBA(packed);
            const error = Math.abs(depth - unpacked);
            
            // 更新深度值显示
            document.getElementById('depthValue').textContent = `深度值: ${depth.toFixed(6)}`;
            
            // 更新打包步骤
            const steps = `
步骤1: 乘以倍数
  ${depth} × 1.0 = ${(depth * 1.0).toFixed(6)}
  ${depth} × 255.0 = ${(depth * 255.0).toFixed(6)}
  ${depth} × 65025.0 = ${(depth * 65025.0).toFixed(6)}
  ${depth} × 16581375.0 = ${(depth * 16581375.0).toFixed(6)}

步骤2: 取小数部分
  fract(${(depth * 1.0).toFixed(6)}) = ${((depth * 1.0) % 1).toFixed(6)}
  fract(${(depth * 255.0).toFixed(6)}) = ${((depth * 255.0) % 1).toFixed(6)}
  fract(${(depth * 65025.0).toFixed(6)}) = ${((depth * 65025.0) % 1).toFixed(6)}
  fract(${(depth * 16581375.0).toFixed(6)}) = ${((depth * 16581375.0) % 1).toFixed(6)}

步骤3: 防止溢出后的最终结果
  R = ${packed[0].toFixed(6)}
  G = ${packed[1].toFixed(6)}
  B = ${packed[2].toFixed(6)}
  A = ${packed[3].toFixed(6)}
            `;
            document.getElementById('packingSteps').textContent = steps;
            
            // 更新通道显示
            document.getElementById('channelR').textContent = packed[0].toFixed(3);
            document.getElementById('channelG').textContent = packed[1].toFixed(3);
            document.getElementById('channelB').textContent = packed[2].toFixed(3);
            document.getElementById('channelA').textContent = packed[3].toFixed(3);
            
            // 更新对比
            document.getElementById('originalDepth').textContent = depth.toFixed(9);
            document.getElementById('unpackedDepth').textContent = unpacked.toFixed(9);
            
            // 更新误差分析
            const errorElement = document.getElementById('errorAnalysis');
            if (error < 1e-6) {
                errorElement.className = 'success-display';
                errorElement.textContent = `✅ 精度误差: ${error.toExponential(3)} (优秀)`;
            } else {
                errorElement.className = 'error-display';
                errorElement.textContent = `⚠️ 精度误差: ${error.toExponential(3)} (需要注意)`;
            }
        }
        
        // 绑定事件
        document.getElementById('depthSlider').addEventListener('input', updateDemo);
        
        // 初始化
        updateDemo();
    </script>
</body>
</html>
