<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Frustum Culling</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info">Frustum Culling. Model by Google Poly</div>
        <script type="module">
            // ========== 导入OGL核心模块 ==========
            import { Renderer, Camera, Transform, Texture, Program, Geometry, Mesh, Vec3, Orbit, Cylinder, NormalProgram } from '../src/index.js';

            // ========== 顶点着色器 ==========
            // 🔬 **功能说明**：
            // 标准的顶点变换着色器，同时传递多种坐标信息给片段着色器。
            // 这些信息用于实现雾效和高度相关的颜色混合。
            const vertex = /* glsl */ `
                // ========== 顶点属性 ==========
                attribute vec2 uv;       // 纹理坐标
                attribute vec3 position; // 顶点位置

                // ========== 变换矩阵 ==========
                uniform mat4 modelViewMatrix;  // 模型视图矩阵
                uniform mat4 projectionMatrix; // 投影矩阵

                // ========== 传递给片段着色器的变量 ==========
                varying vec2 vUv;    // 纹理坐标
                varying vec4 vMVPos; // 模型视图空间位置（用于距离雾效）
                varying vec3 vPos;   // 原始顶点位置（用于高度效果）

                void main() {
                    vUv = uv;
                    vPos = position;

                    // ========== 位置变换 ==========
                    vMVPos = modelViewMatrix * vec4(position, 1.0);  // 变换到视图空间
                    gl_Position = projectionMatrix * vMVPos;         // 变换到裁剪空间
                }
            `;

            // ========== 片段着色器 ==========
            // 🔬 **功能说明**：
            // 实现森林场景的渲染效果，包含纹理采样、距离雾效和高度渐变。
            // 创建逼真的森林氛围，增强视锥体剔除的视觉效果。
            const fragment = /* glsl */ `
                precision highp float;

                // ========== 统一变量 ==========
                uniform sampler2D tMap; // 森林纹理

                // ========== 从顶点着色器传入的变量 ==========
                varying vec2 vUv;    // 纹理坐标
                varying vec4 vMVPos; // 模型视图空间位置
                varying vec3 vPos;   // 原始顶点位置

                void main() {
                    // ========== 纹理采样 ==========
                    vec3 tex = texture2D(tMap, vUv).rgb;

                    // ========== 距离雾效 ==========
                    // 🌫️ **雾效算法**：
                    // 根据片段到相机的距离创建雾效，模拟大气散射。
                    // 距离越远，雾效越强，最终趋向白色。
                    float dist = length(vMVPos);                    // 计算到相机的距离
                    float fog = smoothstep(2.0, 15.0, dist);       // 在2-15单位间平滑过渡
                    tex = mix(tex, vec3(1), fog * 0.8);             // 与白色混合，强度80%

                    // ========== 高度渐变效果 ==========
                    // 🏔️ **地面渐变**：
                    // 根据顶点的Y坐标（高度）创建渐变效果。
                    // 低处（地面）更亮，高处保持原色，模拟地面反射。
                    tex = mix(tex, vec3(1), smoothstep(1.0, 0.0, vPos.y));

                    // ========== 最终颜色输出 ==========
                    gl_FragColor.rgb = tex;
                    gl_FragColor.a = 1.0;
                }
            `;

            // ========== 视锥体剔除演示 ==========
            // 🔬 **核心概念**：
            // 视锥体剔除是3D渲染中的重要优化技术，通过检测对象是否在相机视野内，
            // 避免渲染不可见的对象，从而提升性能。
            //
            // 📐 **视锥体定义**：
            // 视锥体是一个截锥形状的3D区域，由6个平面组成：
            // - 近裁剪面、远裁剪面、左右侧面、上下侧面
            // 只有完全或部分位于视锥体内的对象才需要渲染。

            {
                // ========== 渲染器设置 ==========
                const renderer = new Renderer({ dpr: 2 }); // 高DPI支持
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);
                gl.clearColor(1, 1, 1, 1); // 白色背景，便于观察剔除效果

                // ========== 主相机设置 ==========
                // 🎥 **观察相机**：用户控制的主相机，用于观察整个场景
                const camera = new Camera(gl, { fov: 45 });
                camera.position.set(6, 6, 12); // 设置在较高位置，俯视场景

                // ========== 轨道控制器 ==========
                // 允许用户通过鼠标控制相机，观察视锥体剔除效果
                const controls = new Orbit(camera);

                // ========== 响应式处理 ==========
                function resize() {
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize();

                // ========== 场景根节点 ==========
                const scene = new Transform();

                // ========== 森林纹理加载 ==========
                // 🌲 **森林纹理**：为树木模型提供真实的外观
                const texture = new Texture(gl);
                const img = new Image();
                img.onload = () => (texture.image = img);
                img.src = 'assets/forest.jpg';

                // ========== 森林材质程序 ==========
                const program = new Program(gl, {
                    vertex: vertex, // 使用上面定义的顶点着色器
                    fragment: fragment, // 使用上面定义的片段着色器
                    uniforms: {
                        tMap: { value: texture }, // 森林纹理
                    },
                });

                // ========== 演示用的视锥体相机 ==========
                // 🎯 **关键组件**：这是用于演示视锥体剔除的相机
                // 它会在场景中移动，其视锥体决定哪些对象可见
                const frustumCamera = new Camera(gl, {
                    fov: 65, // 较大的视野角，便于观察剔除效果
                    far: 10, // 较短的远裁剪距离，增强剔除效果
                });
                frustumCamera.target = new Vec3(); // 相机目标点

                // ========== 视锥体相机的可视化变换节点 ==========
                // 🔍 **可视化目的**：显示演示相机的位置和朝向
                const frustumTransform = new Transform();
                frustumTransform.setParent(scene);

                // ========== 初始化场景 ==========
                loadForest(); // 加载森林场景
                addCameraShape(); // 添加相机可视化形状

                // ========== 森林场景加载函数 ==========
                // 🌲 **功能说明**：
                // 异步加载森林模型数据，并创建大量树木实例来演示视锥体剔除效果。
                // 使用网格布局创建400个树木（20×20），每个都有随机的变换。
                async function loadForest() {
                    // ========== 加载模型数据 ==========
                    const data = await (await fetch(`assets/forest.json`)).json();

                    // ========== 网格参数 ==========
                    const size = 20; // 网格尺寸：20×20
                    const num = size * size; // 总数量：400个树木

                    // ========== 创建共享几何体 ==========
                    // 🔧 **性能优化**：所有树木共享同一个几何体，减少内存使用
                    const geometry = new Geometry(gl, {
                        position: { size: 3, data: new Float32Array(data.position) }, // 顶点位置
                        uv: { size: 2, data: new Float32Array(data.uv) }, // 纹理坐标
                    });

                    // ========== 创建树木实例 ==========
                    // 🌳 **实例化**：为每个网格位置创建一个树木网格
                    for (let i = 0; i < num; i++) {
                        const mesh = new Mesh(gl, { geometry, program });
                        mesh.setParent(scene);

                        // ========== 网格位置计算 ==========
                        // 📐 **布局算法**：
                        // - X坐标：(i % size) 得到列索引，减去偏移量居中
                        // - Z坐标：Math.floor(i / size) 得到行索引，减去偏移量居中
                        // - 间距：每个单位间隔2个世界单位
                        mesh.position.set(
                            ((i % size) - size * 0.5) * 2, // X：-19到19，间隔2
                            0, // Y：基础高度
                            (Math.floor(i / size) - size * 0.5) * 2 // Z：-19到19，间隔2
                        );

                        // ========== 地形高度变化 ==========
                        // 🏔️ **程序化地形**：使用正弦函数创建起伏的地形
                        // 两个不同频率的正弦波相乘，创建自然的高度变化
                        mesh.position.y += Math.sin(mesh.position.x * 0.5) * Math.sin(mesh.position.z * 0.5) * 0.5;

                        // ========== 随机变换 ==========
                        mesh.rotation.y = Math.random() * Math.PI * 2; // 随机Y轴旋转
                        mesh.scale.set(0.8 + Math.random() * 0.3); // 随机缩放：0.8-1.1
                    }
                }

                // ========== 相机形状可视化函数 ==========
                // 🎥 **功能说明**：
                // 创建一个圆柱体来可视化演示相机的位置和朝向。
                // 这样用户可以清楚地看到哪个相机在执行视锥体剔除。
                function addCameraShape() {
                    // ========== 创建圆柱体几何体 ==========
                    // 📐 **形状设计**：使用4边形圆柱体模拟相机外观
                    const mesh = new Mesh(gl, {
                        geometry: new Cylinder(gl, {
                            radiusBottom: 0.2, // 底部半径
                            height: 0.7, // 高度
                            radialSegments: 4, // 4边形（方形截面）
                            openEnded: true, // 开放式（无顶底面）
                        }),
                        program: new NormalProgram(gl), // 法线着色器（彩色显示）
                    });

                    // ========== 渲染设置 ==========
                    mesh.program.cullFace = false; // 禁用背面剔除，确保可见

                    // ========== 层级关系 ==========
                    mesh.setParent(frustumTransform); // 作为视锥体变换的子节点
                    mesh.isCameraShape = true; // 标记为相机形状（避免被剔除）

                    // ========== 旋转设置 ==========
                    // 🔄 **朝向调整**：调整圆柱体朝向，使其看起来像相机
                    mesh.rotation.reorder('XYZ'); // 设置旋转顺序
                    mesh.rotation.x = -Math.PI / 2; // X轴旋转90度（竖直变水平）
                    mesh.rotation.y = Math.PI / 4; // Y轴旋转45度（斜向）
                }

                // ========== 相机路径函数 ==========
                // 🛤️ **功能说明**：
                // 定义演示相机的运动轨迹，使用参数化的数学函数创建平滑的路径。
                // 相机和目标点都沿着不同的轨迹移动，创建动态的视角变化。
                function cameraPath(vec, time, y) {
                    // ========== 路径计算 ==========
                    // 📐 **数学轨迹**：
                    // - X轴：使用sin(t)创建左右摆动，幅度为4
                    // - Z轴：使用sin(2t)创建前后移动，幅度为2，频率是X轴的2倍
                    // - Y轴：由参数指定，保持恒定高度
                    const x = 4 * Math.sin(time); // 水平摆动：-4到4
                    const z = 2 * Math.sin(time * 2); // 前后移动：-2到2，频率更高
                    vec.set(x, y, z);
                }

                // ========== 动画循环 ==========
                requestAnimationFrame(update);
                function update(t) {
                    requestAnimationFrame(update);

                    // ========== 演示相机动画 ==========
                    // 🎬 **相机运动**：让演示相机沿着预定义路径移动
                    // 相机位置和目标点使用不同的时间偏移，创建更自然的运动
                    cameraPath(frustumCamera.position, t * 0.001, 2); // 相机位置：高度2
                    cameraPath(frustumCamera.target, t * 0.001 + 1, 1); // 目标点：高度1，时间偏移1秒

                    // ========== 相机矩阵更新 ==========
                    // 🔄 **关键步骤**：更新相机的变换矩阵和视锥体
                    frustumCamera.lookAt(frustumCamera.target); // 让相机朝向目标点
                    frustumCamera.updateMatrixWorld(); // 更新世界变换矩阵
                    frustumCamera.updateFrustum(); // ⭐ 更新视锥体平面方程

                    // ========== 同步可视化形状 ==========
                    // 🎥 **可视化同步**：让相机形状跟随演示相机的位置和旋转
                    frustumTransform.position.copy(frustumCamera.position); // 同步位置
                    frustumTransform.rotation.copy(frustumCamera.rotation); // 同步旋转

                    // ========== 视锥体剔除测试 ==========
                    // 🔬 **核心算法**：遍历场景中的所有对象，执行视锥体剔除测试
                    scene.traverse((node) => {
                        // ========== 过滤条件 ==========
                        if (!node.draw) return; // 跳过非渲染对象
                        if (node.isCameraShape) return; // 跳过相机形状本身

                        // ========== 视锥体相交测试 ==========
                        // 🎯 **关键操作**：检测网格是否与视锥体相交
                        //
                        // 📐 **测试原理**：
                        // 1. 获取网格的包围盒（AABB）
                        // 2. 将包围盒变换到世界空间
                        // 3. 测试包围盒是否与视锥体的6个平面相交
                        // 4. 如果包围盒完全在任一平面外侧，则不可见
                        // 5. 否则认为可见（包括部分可见的情况）
                        //
                        // 🔧 **OGL实现**：
                        // frustumIntersectsMesh() 方法内部执行以下步骤：
                        // - 计算网格的世界空间包围盒
                        // - 对每个视锥体平面进行点-平面距离测试
                        // - 使用包围盒的8个顶点进行测试
                        // - 返回布尔值表示是否可见
                        node.visible = frustumCamera.frustumIntersectsMesh(node);
                    });

                    // ========== 渲染场景 ==========
                    controls.update(); // 更新轨道控制器
                    renderer.render({ scene, camera }); // 渲染场景（使用主相机）
                }
            }
        </script>
    </body>
</html>
