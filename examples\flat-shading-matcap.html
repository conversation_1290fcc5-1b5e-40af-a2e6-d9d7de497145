<!DOCTYPE html>
<html lang="en">
    <head>
        <!-- 设置文档字符编码为UTF-8 -->
        <meta charset="UTF-8" />
        <!-- 设置IE浏览器兼容性，使用最新的渲染引擎 -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <!-- 移动端视口设置：禁用缩放，适配全屏显示 -->
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <!-- 设置网站图标 -->
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <!-- 页面标题：OGL平面着色Matcap效果演示 -->
        <title>OGL • Flat Shading Matcap</title>
        <!-- 引入主样式表 -->
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <!-- 页面信息提示：展示平面着色Matcap效果，模型来自Google Poly -->
        <div class="Info">Flat Shading Matcap. Model by Google Poly.</div>
        <script type="module">
            // 从OGL库导入所需的WebGL组件
            // Renderer: 渲染器，负责WebGL上下文管理和渲染
            // Camera: 相机，定义视角和投影
            // Transform: 变换节点，用于场景图管理
            // Texture: 纹理，用于材质贴图
            // Program: 着色器程序，包含顶点和片段着色器
            // Geometry: 几何体，定义顶点数据
            // Mesh: 网格，结合几何体和着色器程序
            // Vec3: 三维向量，用于位置和方向计算
            // Orbit: 轨道控制器，实现相机交互控制
            import { Renderer, Camera, Transform, Texture, Program, Geometry, Mesh, Vec3, Orbit } from '../src/index.js';

            // WebGL版本兼容性说明：
            // 当使用标准导数函数（dFdx & dFdy）时，这些函数对于平面着色效果是必需的
            // WebGL1需要GL_OES_standard_derivatives扩展支持
            // WebGL2内置支持但会对扩展声明报错
            // 因此需要为WebGL2创建300 es版本的GLSL着色器，为WebGL1创建100 es版本
            // 两个版本之间只有轻微的语法差异

            // 顶点着色器（基础版本，稍后会根据WebGL版本添加版本声明）
            const vertex = /* glsl */ `
                // 输入：顶点位置属性（来自几何体的position数据）
                attribute vec3 position;

                // 统一变量：模型视图矩阵（将模型坐标转换为视图坐标）
                uniform mat4 modelViewMatrix;
                // 统一变量：投影矩阵（将视图坐标转换为裁剪坐标）
                uniform mat4 projectionMatrix;

                // 输出：模型视图空间中的位置（传递给片段着色器）
                varying vec4 vMVPos;

                void main() {
                    // 将顶点位置转换到模型视图空间
                    // 这个位置将用于片段着色器中计算法线
                    vMVPos = modelViewMatrix * vec4(position, 1.0);

                    // 计算最终的裁剪空间位置
                    gl_Position = projectionMatrix * vMVPos;
                }
            `;

            // 片段着色器（基础版本）
            const fragment = /* glsl */ `
                // 统一变量：Matcap纹理采样器
                uniform sampler2D tMap;

                // 输入：从顶点着色器传递的模型视图空间位置
                varying vec4 vMVPos;

                // 平面着色法线计算函数
                // 使用屏幕空间导数计算每个像素的法线向量
                // 这是平面着色的核心：不使用预计算的法线，而是在片段着色器中动态计算
                vec3 normals(vec3 pos) {
                    // dFdx: 计算位置在x方向的偏导数（相邻像素间的差值）
                    vec3 fdx = dFdx(pos);
                    // dFdy: 计算位置在y方向的偏导数
                    vec3 fdy = dFdy(pos);
                    // 通过叉积计算法线向量，并归一化
                    // 叉积得到垂直于两个向量的向量，即表面法线
                    return normalize(cross(fdx, fdy));
                }

                // Matcap纹理坐标计算函数
                // Matcap（Material Capture）是一种基于法线的材质技术
                // 通过将法线映射到纹理坐标来模拟复杂的光照效果
                vec2 matcap(vec3 eye, vec3 normal) {
                    // 计算视线方向相对于法线的反射向量
                    vec3 reflected = reflect(eye, normal);

                    // 球面映射公式：将3D反射向量映射到2D纹理坐标
                    // 2.8284271247461903 = 2 * sqrt(2)，用于球面映射的数学常数
                    float m = 2.8284271247461903 * sqrt(reflected.z + 1.0);

                    // 将反射向量的xy分量映射到[0,1]范围的纹理坐标
                    return reflected.xy / m + 0.5;
                }

                void main() {
                    // 计算当前像素的平面着色法线
                    vec3 normal = normals(vMVPos.xyz);

                    // 使用Matcap纹理为模型添加光泽效果
                    // 归一化视图空间位置作为视线方向
                    // 采样Matcap纹理的绿色通道作为材质值
                    float mat = texture2D(tMap, matcap(normalize(vMVPos.xyz), normal)).g;

                    // 最终颜色 = 法线颜色 + Matcap材质效果
                    // 法线作为基础颜色，Matcap提供额外的光泽和反射效果
                    gl_FragColor.rgb = normal + mat;
                    gl_FragColor.a = 1.0;
                }
            `;

            // WebGL1版本的顶点着色器（GLSL 100 ES）
            // 直接使用基础顶点着色器代码，无需额外的版本声明
            const vertex100 =
                /* glsl */ `
            ` + vertex;

            // WebGL1版本的片段着色器（GLSL 100 ES）
            // 需要显式启用标准导数扩展，并设置精度
            const fragment100 =
                /* glsl */ `#extension GL_OES_standard_derivatives : enable
                precision highp float;
            ` + fragment;

            // WebGL2版本的顶点着色器（GLSL 300 ES）
            // 使用新的GLSL语法：attribute -> in, varying -> out
            const vertex300 =
                /* glsl */ `#version 300 es
                #define attribute in
                #define varying out
            ` + vertex;

            // WebGL2版本的片段着色器（GLSL 300 ES）
            // 使用新的GLSL语法和内置函数
            const fragment300 =
                /* glsl */ `#version 300 es
                precision highp float;
                #define varying in          // varying -> in（片段着色器中）
                #define texture2D texture   // texture2D -> texture
                #define gl_FragColor FragColor  // gl_FragColor -> 自定义输出变量
                out vec4 FragColor;         // 声明片段着色器输出
            ` + fragment;

            {
                // 创建WebGL渲染器
                // dpr: 2 设置设备像素比为2，提供高分辨率渲染（适配高DPI屏幕）
                const renderer = new Renderer({ dpr: 2 });

                // 获取WebGL上下文
                const gl = renderer.gl;

                // 将canvas元素添加到页面body中
                document.body.appendChild(gl.canvas);

                // 设置清除颜色为白色（RGBA: 1,1,1,1）
                // 这是渲染每帧前清除画布的背景色
                gl.clearColor(1, 1, 1, 1);

                // 创建透视相机
                // fov: 45 设置视野角度为45度
                const camera = new Camera(gl, { fov: 45 });

                // 设置相机位置：x=2, y=1, z=2
                // 这个位置让相机从右上前方观察场景中心
                camera.position.set(2, 1, 2);

                // 创建轨道控制器，实现鼠标交互控制相机
                const controls = new Orbit(camera, {
                    // 设置相机注视目标点：稍微偏上的位置(0, 0.2, 0)
                    target: new Vec3(0, 0.2, 0),
                });

                // 窗口大小调整处理函数
                function resize() {
                    // 更新渲染器尺寸为窗口大小
                    renderer.setSize(window.innerWidth, window.innerHeight);

                    // 更新相机投影矩阵的宽高比
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }

                // 监听窗口大小变化事件
                window.addEventListener('resize', resize, false);

                // 初始化时调用一次resize，设置正确的画布尺寸
                resize();

                // 创建场景根节点
                // Transform作为场景图的根节点，所有3D对象都将作为其子节点
                const scene = new Transform();

                // 创建Matcap纹理对象
                const texture = new Texture(gl);

                // 创建图像对象用于加载Matcap纹理
                const img = new Image();

                // 图像加载完成后，将其设置为纹理的图像数据
                // Matcap纹理包含预烘焙的光照信息，用于实现快速的材质效果
                img.onload = () => (texture.image = img);

                // 加载Matcap纹理文件
                // 这个纹理包含了球形表面在不同角度下的光照效果
                img.src = 'assets/matcap.jpg';

                // 创建着色器程序
                const program = new Program(gl, {
                    // 根据WebGL版本选择对应的顶点着色器
                    vertex: renderer.isWebgl2 ? vertex300 : vertex100,

                    // 根据WebGL版本选择对应的片段着色器
                    fragment: renderer.isWebgl2 ? fragment300 : fragment100,

                    // 统一变量：传递给着色器的全局参数
                    uniforms: {
                        // tMap: Matcap纹理，用于材质效果计算
                        tMap: { value: texture },
                    },

                    // 禁用面剔除，确保模型的所有面都被渲染
                    // 这对于某些模型（如内部结构可见的模型）很重要
                    cullFace: false,
                });

                // 调用模型加载函数
                loadModel();

                // 异步加载3D模型数据
                async function loadModel() {
                    // 从JSON文件加载章鱼模型的顶点数据
                    const data = await (await fetch(`assets/octopus.json`)).json();

                    // 平面着色技术说明：
                    // 如果能够预先生成平面着色法线作为顶点属性，会更高效
                    // 但是如果网格是动态的、使用索引的，或者在着色器中更新顶点
                    // 我们仍然可以在着色器中计算法线 - 只需要位置数据即可
                    // 这就是本示例采用的方法：仅使用位置数据，在片段着色器中动态计算法线

                    // 创建几何体，只包含位置数据
                    const geometry = new Geometry(gl, {
                        // position: 顶点位置数据
                        // size: 3 表示每个顶点有3个分量(x, y, z)
                        // data: 将JSON数据转换为Float32Array格式
                        position: { size: 3, data: new Float32Array(data.position) },
                    });

                    // 创建网格对象，结合几何体和着色器程序
                    let mesh = new Mesh(gl, { geometry, program });

                    // 将网格添加到场景中
                    mesh.setParent(scene);
                }

                // 启动渲染循环
                requestAnimationFrame(update);

                // 渲染循环函数
                function update() {
                    // 请求下一帧动画，形成连续的渲染循环
                    requestAnimationFrame(update);

                    // 更新轨道控制器（处理鼠标交互）
                    controls.update();

                    // 执行渲染：将场景通过相机渲染到画布
                    renderer.render({ scene, camera });
                }
            }
        </script>
    </body>
</html>
