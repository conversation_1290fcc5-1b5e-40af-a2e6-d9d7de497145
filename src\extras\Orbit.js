/**
 * 轨道控制器 (Orbit Controls)
 *
 * 基于ThreeJS的OrbitControls类，使用ES6重写，并进行了一些增减修改
 *
 * 功能特性：
 * - 支持鼠标和触摸设备的交互操作
 * - 围绕目标点进行球坐标系旋转
 * - 支持两种缩放模式：相机位移(dolly)和视野角度变化(fov)
 * - 平滑的缓动和惯性效果
 * - 可配置的操作限制和边界
 * - 自动旋转功能
 *
 * 数学原理：
 * - 使用球坐标系(r, θ, φ)表示相机相对于目标点的位置
 * - θ (theta): 方位角，控制水平旋转 (绕Y轴)
 * - φ (phi): 极角，控制垂直旋转 (从Y轴向下的角度)
 * - r (radius): 径向距离，控制缩放
 *
 * 坐标转换：
 * - 球坐标 → 笛卡尔坐标：
 *   x = r * sin(φ) * sin(θ)
 *   y = r * cos(φ)
 *   z = r * sin(φ) * cos(θ)
 *
 * TODO: 抽象事件处理器，使其可以从其他来源获取输入
 * TODO: 使滚轮缩放比简单的 >/< 零判断更精确
 * TODO: 能够传入新的相机位置
 */

import { Vec3 } from '../math/Vec3.js';
import { Vec2 } from '../math/Vec2.js';

/**
 * 轨道控制器状态枚举
 * NONE: 无操作状态
 * ROTATE: 旋转状态
 * DOLLY: 缩放状态
 * PAN: 平移状态
 * DOLLY_PAN: 同时缩放和平移状态（触摸设备上的多指操作）
 */
const STATE = { NONE: -1, ROTATE: 0, DOLLY: 1, PAN: 2, DOLLY_PAN: 3 };

// 预先创建临时变量以避免重复创建对象
const tempVec3 = /* @__PURE__ */ new Vec3();
const tempVec2a = /* @__PURE__ */ new Vec2();
const tempVec2b = /* @__PURE__ */ new Vec2();

/**
 * 轨道控制器
 * 允许用户通过鼠标或触摸操作来旋转、缩放和平移相机
 *
 * @param {Object} object - 要控制的对象（通常是相机）
 * @param {Object} options - 配置选项
 */
export function Orbit(
    object,
    {
        element = document, // 监听事件的DOM元素
        enabled = true, // 是否启用控制器
        target = new Vec3(), // 相机围绕旋转的目标点
        ease = 0.25, // 缓动系数，控制相机移动的平滑度
        inertia = 0.85, // 惯性系数，控制相机停止后的滑动效果
        enableRotate = true, // 是否启用旋转
        rotateSpeed = 0.1, // 旋转速度
        autoRotate = false, // 是否自动旋转
        autoRotateSpeed = 1.0, // 自动旋转速度
        enableZoom = true, // 是否启用缩放
        zoomSpeed = 1, // 缩放速度
        zoomStyle = 'dolly', // 缩放方式：'dolly'（移动相机）或改变FOV
        enablePan = true, // 是否启用平移
        panSpeed = 0.1, // 平移速度
        minPolarAngle = 0, // 最小极角（垂直旋转限制）
        maxPolarAngle = Math.PI, // 最大极角
        minAzimuthAngle = -Infinity, // 最小方位角（水平旋转限制）
        maxAzimuthAngle = Infinity, // 最大方位角
        minDistance = 0, // 最小距离（缩放限制）
        maxDistance = Infinity, // 最大距离
    } = {}
) {
    this.enabled = enabled;
    this.target = target;
    this.zoomStyle = zoomStyle;

    // 处理禁用尝试 - 设为1使其无效果
    ease = ease || 1;
    inertia = inertia || 0;

    this.minDistance = minDistance;
    this.maxDistance = maxDistance;

    // 球坐标系中的当前位置和目标位置
    const sphericalDelta = { radius: 1, phi: 0, theta: 0 }; // 球坐标增量
    const sphericalTarget = { radius: 1, phi: 0, theta: 0 }; // 目标球坐标
    const spherical = { radius: 1, phi: 0, theta: 0 }; // 当前球坐标
    const panDelta = new Vec3(); // 平移增量

    // === 初始化球坐标系 ===
    // 计算相机相对于目标点的偏移向量
    const offset = new Vec3();
    offset.copy(object.position).sub(this.target);

    // 将笛卡尔坐标转换为球坐标
    // r: 径向距离 = 向量的长度
    spherical.radius = sphericalTarget.radius = offset.distance();

    // θ: 方位角 = atan2(x, z) - 在XZ平面上的角度
    // 使用atan2确保角度在正确的象限内
    spherical.theta = sphericalTarget.theta = Math.atan2(offset.x, offset.z);

    // φ: 极角 = acos(y/r) - 从Y轴正方向向下的角度
    // clamp到[-1,1]范围避免acos的域错误
    spherical.phi = sphericalTarget.phi = Math.acos(Math.min(Math.max(offset.y / sphericalTarget.radius, -1), 1));

    this.offset = offset;

    /**
     * 更新相机位置和旋转
     * 根据当前的增量值计算新的相机位置
     */
    this.update = () => {
        // 如果启用了自动旋转，则处理自动旋转
        if (autoRotate) {
            handleAutoRotate();
        }

        // 应用增量到目标球坐标
        sphericalTarget.radius *= sphericalDelta.radius;
        sphericalTarget.theta += sphericalDelta.theta;
        sphericalTarget.phi += sphericalDelta.phi;

        // 应用边界限制
        sphericalTarget.theta = Math.max(minAzimuthAngle, Math.min(maxAzimuthAngle, sphericalTarget.theta));
        sphericalTarget.phi = Math.max(minPolarAngle, Math.min(maxPolarAngle, sphericalTarget.phi));
        sphericalTarget.radius = Math.max(this.minDistance, Math.min(this.maxDistance, sphericalTarget.radius));

        // 应用缓动效果
        spherical.phi += (sphericalTarget.phi - spherical.phi) * ease;
        spherical.theta += (sphericalTarget.theta - spherical.theta) * ease;
        spherical.radius += (sphericalTarget.radius - spherical.radius) * ease;

        // 应用平移到目标点。由于偏移量是相对于目标的，它也会移动
        this.target.add(panDelta);

        // === 球坐标转换为笛卡尔坐标 ===
        // 球坐标系到笛卡尔坐标系的标准转换公式：
        // x = r * sin(φ) * sin(θ)
        // y = r * cos(φ)
        // z = r * sin(φ) * cos(θ)

        // 预计算 r * sin(φ) 以提高性能
        // Math.max(0.000001, spherical.phi) 防止sin(φ)为0导致的奇点问题
        let sinPhiRadius = spherical.radius * Math.sin(Math.max(0.000001, spherical.phi));

        offset.x = sinPhiRadius * Math.sin(spherical.theta); // X分量：水平圆周运动
        offset.y = spherical.radius * Math.cos(spherical.phi); // Y分量：垂直高度
        offset.z = sinPhiRadius * Math.cos(spherical.theta); // Z分量：深度圆周运动

        // 将更新后的值应用到对象
        object.position.copy(this.target).add(offset);
        object.lookAt(this.target);

        // 应用惯性到增量值
        sphericalDelta.theta *= inertia;
        sphericalDelta.phi *= inertia;
        panDelta.multiply(inertia);

        // 每帧重置缩放，避免多次应用缩放
        sphericalDelta.radius = 1;
    };

    /**
     * 强制更新内部状态以匹配当前位置
     * 用于在外部修改相机位置后同步控制器状态
     */
    this.forcePosition = () => {
        offset.copy(object.position).sub(this.target);
        spherical.radius = sphericalTarget.radius = offset.distance();
        spherical.theta = sphericalTarget.theta = Math.atan2(offset.x, offset.z);
        spherical.phi = sphericalTarget.phi = Math.acos(Math.min(Math.max(offset.y / sphericalTarget.radius, -1), 1));
        object.lookAt(this.target);
    };

    // 以下代码用于更新panDelta和sphericalDelta
    // 使用这两个对象的值来计算轨道

    // 用于跟踪交互起始点的向量
    const rotateStart = new Vec2(); // 旋转起始点
    const panStart = new Vec2(); // 平移起始点
    const dollyStart = new Vec2(); // 缩放起始点

    // 当前控制器状态
    let state = STATE.NONE;
    // 鼠标按钮映射
    this.mouseButtons = { ORBIT: 0, ZOOM: 1, PAN: 2 };

    /**
     * 获取缩放比例
     *
     * 使用指数函数计算缩放因子，提供平滑的缩放体验
     * 基数0.95意味着每次缩放操作会将距离缩减到原来的95%
     * zoomSpeed控制缩放的敏感度：
     * - zoomSpeed = 1: 标准缩放速度
     * - zoomSpeed > 1: 更快的缩放
     * - zoomSpeed < 1: 更慢的缩放
     *
     * @returns {Number} 缩放比例因子 (0 < factor < 1 表示缩小)
     */
    function getZoomScale() {
        return Math.pow(0.95, zoomSpeed);
    }

    /**
     * 向左平移相机
     *
     * 通过提取相机变换矩阵的右向量来实现屏幕空间的左右平移
     * 变换矩阵的列向量表示局部坐标系的基向量：
     * - 第1列 [m[0], m[1], m[2]]: 右向量 (local X轴)
     * - 第2列 [m[4], m[5], m[6]]: 上向量 (local Y轴)
     * - 第3列 [m[8], m[9], m[10]]: 前向量 (local Z轴)
     *
     * @param {Number} distance - 平移距离
     * @param {Array} m - 对象的变换矩阵（4x4矩阵的16个元素）
     */
    function panLeft(distance, m) {
        tempVec3.set(m[0], m[1], m[2]); // 获取矩阵的第一列（右向量）
        tempVec3.multiply(-distance); // 向左移动（负右向量方向）
        panDelta.add(tempVec3); // 累加到平移增量
    }

    /**
     * 向上平移相机
     *
     * 通过提取相机变换矩阵的上向量来实现屏幕空间的上下平移
     *
     * @param {Number} distance - 平移距离
     * @param {Array} m - 对象的变换矩阵（4x4矩阵的16个元素）
     */
    function panUp(distance, m) {
        tempVec3.set(m[4], m[5], m[6]); // 获取矩阵的第二列（上向量）
        tempVec3.multiply(distance); // 向上移动（正上向量方向）
        panDelta.add(tempVec3); // 累加到平移增量
    }

    /**
     * 平移相机
     *
     * 实现屏幕空间到世界空间的平移映射
     * 平移距离需要根据相机到目标的距离和视野角度进行缩放，
     * 以确保在不同缩放级别下平移的视觉速度保持一致
     *
     * 数学原理：
     * 1. 计算相机到目标点的距离
     * 2. 根据FOV计算在目标距离处的视野宽度
     * 3. 将屏幕像素位移转换为世界空间位移
     *
     * @param {Number} deltaX - X轴平移量（屏幕像素）
     * @param {Number} deltaY - Y轴平移量（屏幕像素）
     */
    const pan = (deltaX, deltaY) => {
        let el = element === document ? document.body : element;

        // 计算相机到目标点的距离
        tempVec3.copy(object.position).sub(this.target);
        let targetDistance = tempVec3.distance();

        // 根据FOV和距离计算视野在目标平面上的实际尺寸
        // tan(fov/2) * distance = 视野半高度
        targetDistance *= Math.tan((((object.fov || 45) / 2) * Math.PI) / 180.0);

        // 将屏幕像素位移转换为世界空间位移
        // 使用clientHeight作为参考，因为FOV通常定义垂直视野角
        panLeft((2 * deltaX * targetDistance) / el.clientHeight, object.matrix);
        panUp((2 * deltaY * targetDistance) / el.clientHeight, object.matrix);
    };

    /**
     * 缩放相机
     *
     * 提供两种不同的缩放实现方式：
     *
     * 1. Dolly模式（物理缩放）：
     *    - 通过改变相机到目标点的距离实现缩放
     *    - 模拟真实世界中的前进/后退运动
     *    - 透视效果会随距离变化而改变
     *    - 适合需要真实物理感的场景
     *
     * 2. FOV模式（光学缩放）：
     *    - 通过改变相机的视野角度实现缩放
     *    - 模拟变焦镜头的效果
     *    - 相机位置保持不变，只改变视野范围
     *    - 适合需要保持相机位置的场景
     *
     * @param {Number} dollyScale - 缩放比例（>1放大，<1缩小）
     */
    const dolly = (dollyScale) => {
        if (this.zoomStyle === 'dolly') {
            // Dolly模式：改变相机距离
            // 除法操作：dollyScale > 1 时距离减小（放大），< 1 时距离增大（缩小）
            sphericalDelta.radius /= dollyScale;
        } else {
            // FOV模式：改变视野角度
            // 除法操作：dollyScale > 1 时FOV减小（放大），< 1 时FOV增大（缩小）
            object.fov /= dollyScale;

            // 更新投影矩阵以应用新的FOV
            if (object.type === 'orthographic') object.orthographic();
            else object.perspective();
        }
    };

    /**
     * 处理自动旋转
     *
     * 计算每帧的自动旋转角度增量
     * 假设60FPS的帧率，每秒完成 autoRotateSpeed 圈旋转
     *
     * 计算公式：
     * - 一圈 = 2π 弧度
     * - 每秒旋转 autoRotateSpeed 圈
     * - 每帧旋转角度 = (2π * autoRotateSpeed) / (60 FPS)
     * - 简化为：(2π / 60 / 60) * autoRotateSpeed
     */
    function handleAutoRotate() {
        // 计算每帧的旋转角度（弧度）
        const angle = ((2 * Math.PI) / 60 / 60) * autoRotateSpeed;
        // 减去角度实现顺时针旋转（从上方俯视）
        sphericalDelta.theta -= angle;
    }

    /**
     * 处理旋转移动
     *
     * 将屏幕空间的鼠标移动转换为球坐标系的角度变化
     *
     * 映射关系：
     * - 水平移动 → θ (方位角) 变化：左右旋转
     * - 垂直移动 → φ (极角) 变化：上下旋转
     *
     * 使用clientHeight作为归一化参考，确保旋转速度与屏幕尺寸无关
     *
     * @param {Number} x - 当前鼠标X坐标
     * @param {Number} y - 当前鼠标Y坐标
     */
    function handleMoveRotate(x, y) {
        tempVec2a.set(x, y);

        // 计算鼠标移动距离并应用旋转速度系数
        tempVec2b.sub(tempVec2a, rotateStart).multiply(rotateSpeed);
        let el = element === document ? document.body : element;

        // 将像素位移转换为弧度角度变化
        // 使用clientHeight确保在不同屏幕尺寸下旋转速度一致
        sphericalDelta.theta -= (2 * Math.PI * tempVec2b.x) / el.clientHeight; // 水平旋转
        sphericalDelta.phi -= (2 * Math.PI * tempVec2b.y) / el.clientHeight; // 垂直旋转

        // 更新旋转起始点为当前位置，准备下次计算增量
        rotateStart.copy(tempVec2a);
    }

    /**
     * 处理鼠标缩放移动
     * @param {MouseEvent} e - 鼠标事件
     */
    function handleMouseMoveDolly(e) {
        tempVec2a.set(e.clientX, e.clientY);
        tempVec2b.sub(tempVec2a, dollyStart);
        // 根据鼠标Y轴移动方向决定缩放方向
        if (tempVec2b.y > 0) {
            dolly(getZoomScale()); // 放大
        } else if (tempVec2b.y < 0) {
            dolly(1 / getZoomScale()); // 缩小
        }
        dollyStart.copy(tempVec2a);
    }

    /**
     * 处理平移移动
     * @param {Number} x - 鼠标X坐标
     * @param {Number} y - 鼠标Y坐标
     */
    function handleMovePan(x, y) {
        tempVec2a.set(x, y);
        // 计算鼠标移动距离并应用平移速度
        tempVec2b.sub(tempVec2a, panStart).multiply(panSpeed);
        // 执行平移
        pan(tempVec2b.x, tempVec2b.y);
        // 更新平移起始点
        panStart.copy(tempVec2a);
    }

    /**
     * 处理触摸开始时的缩放和平移
     *
     * 双指触摸操作的初始化：
     * 1. 缩放：记录两指间的初始距离，后续通过距离变化计算缩放比例
     * 2. 平移：记录两指的中心点，后续通过中心点移动计算平移量
     *
     * 这种设计允许用户同时进行缩放和平移操作，提供流畅的触摸体验
     *
     * @param {TouchEvent} e - 触摸事件，包含多个触摸点信息
     */
    function handleTouchStartDollyPan(e) {
        if (enableZoom) {
            // 计算两个触摸点之间的欧几里得距离
            // 这个距离将作为缩放操作的基准
            let dx = e.touches[0].pageX - e.touches[1].pageX;
            let dy = e.touches[0].pageY - e.touches[1].pageY;
            let distance = Math.sqrt(dx * dx + dy * dy);
            dollyStart.set(0, distance); // X分量不使用，Y分量存储距离
        }

        if (enablePan) {
            // 计算两个触摸点的几何中心
            // 中心点的移动将驱动平移操作
            let x = 0.5 * (e.touches[0].pageX + e.touches[1].pageX);
            let y = 0.5 * (e.touches[0].pageY + e.touches[1].pageY);
            panStart.set(x, y);
        }
    }

    /**
     * 处理触摸移动时的缩放和平移
     *
     * 双指触摸的动态处理：
     *
     * 缩放逻辑：
     * 1. 计算当前两指间距离
     * 2. 与初始距离比较得到缩放比例
     * 3. 使用幂函数调整缩放敏感度
     *
     * 平移逻辑：
     * 1. 计算当前两指中心点
     * 2. 与初始中心点比较得到位移量
     * 3. 调用统一的平移处理函数
     *
     * @param {TouchEvent} e - 触摸事件，包含当前触摸点位置
     */
    function handleTouchMoveDollyPan(e) {
        if (enableZoom) {
            // 重新计算两个触摸点之间的距离
            let dx = e.touches[0].pageX - e.touches[1].pageX;
            let dy = e.touches[0].pageY - e.touches[1].pageY;
            let distance = Math.sqrt(dx * dx + dy * dy);
            tempVec2a.set(0, distance);

            // 计算缩放比例：当前距离 / 初始距离
            // 使用Math.pow应用zoomSpeed作为指数，调整缩放敏感度
            // zoomSpeed > 1: 更敏感的缩放
            // zoomSpeed < 1: 更平缓的缩放
            tempVec2b.set(0, Math.pow(tempVec2a.y / dollyStart.y, zoomSpeed));
            dolly(tempVec2b.y);
            dollyStart.copy(tempVec2a); // 更新基准距离
        }

        if (enablePan) {
            // 计算两个触摸点的新中心点
            let x = 0.5 * (e.touches[0].pageX + e.touches[1].pageX);
            let y = 0.5 * (e.touches[0].pageY + e.touches[1].pageY);
            // 使用统一的平移处理函数
            handleMovePan(x, y);
        }
    }

    /**
     * 鼠标按下事件处理
     * @param {MouseEvent} e - 鼠标事件
     */
    const onMouseDown = (e) => {
        if (!this.enabled) return;

        // 根据按下的鼠标按钮确定操作类型
        switch (e.button) {
            case this.mouseButtons.ORBIT:
                if (enableRotate === false) return;
                rotateStart.set(e.clientX, e.clientY);
                state = STATE.ROTATE;
                break;
            case this.mouseButtons.ZOOM:
                if (enableZoom === false) return;
                dollyStart.set(e.clientX, e.clientY);
                state = STATE.DOLLY;
                break;
            case this.mouseButtons.PAN:
                if (enablePan === false) return;
                panStart.set(e.clientX, e.clientY);
                state = STATE.PAN;
                break;
        }

        // 如果有操作，添加鼠标移动和抬起事件监听
        if (state !== STATE.NONE) {
            window.addEventListener('mousemove', onMouseMove, false);
            window.addEventListener('mouseup', onMouseUp, false);
        }
    };

    /**
     * 鼠标移动事件处理
     * @param {MouseEvent} e - 鼠标事件
     */
    const onMouseMove = (e) => {
        if (!this.enabled) return;

        // 根据当前状态执行相应的操作
        switch (state) {
            case STATE.ROTATE:
                if (enableRotate === false) return;
                handleMoveRotate(e.clientX, e.clientY);
                break;
            case STATE.DOLLY:
                if (enableZoom === false) return;
                handleMouseMoveDolly(e);
                break;
            case STATE.PAN:
                if (enablePan === false) return;
                handleMovePan(e.clientX, e.clientY);
                break;
        }
    };

    /**
     * 鼠标抬起事件处理
     */
    const onMouseUp = () => {
        // 移除临时事件监听
        window.removeEventListener('mousemove', onMouseMove, false);
        window.removeEventListener('mouseup', onMouseUp, false);
        // 重置状态
        state = STATE.NONE;
    };

    /**
     * 鼠标滚轮事件处理
     *
     * 滚轮缩放的实现逻辑：
     * 1. 检查操作权限和状态兼容性
     * 2. 阻止默认滚动行为和事件冒泡
     * 3. 根据滚轮方向应用相应的缩放
     *
     * 状态兼容性：
     * - 允许在NONE状态下进行滚轮缩放
     * - 允许在ROTATE状态下进行滚轮缩放（旋转+缩放组合操作）
     * - 禁止在其他状态下进行滚轮缩放，避免操作冲突
     *
     * @param {WheelEvent} e - 滚轮事件，包含滚动方向信息
     */
    const onMouseWheel = (e) => {
        // 状态检查：确保控制器启用、缩放启用、且状态兼容
        if (!this.enabled || !enableZoom || (state !== STATE.NONE && state !== STATE.ROTATE)) return;

        // 阻止浏览器默认的滚动行为和事件冒泡
        e.stopPropagation();
        e.preventDefault();

        // 根据滚轮方向执行缩放
        // deltaY < 0: 向上滚动，放大（缩放因子 > 1）
        // deltaY > 0: 向下滚动，缩小（缩放因子 < 1）
        if (e.deltaY < 0) {
            dolly(1 / getZoomScale()); // 放大：使用缩放因子的倒数
        } else if (e.deltaY > 0) {
            dolly(getZoomScale()); // 缩小：直接使用缩放因子
        }
    };

    /**
     * 触摸开始事件处理
     * @param {TouchEvent} e - 触摸事件
     */
    const onTouchStart = (e) => {
        if (!this.enabled) return;
        e.preventDefault();

        // 根据触摸点数量确定操作类型
        switch (e.touches.length) {
            case 1: // 单指触摸 - 旋转
                if (enableRotate === false) return;
                rotateStart.set(e.touches[0].pageX, e.touches[0].pageY);
                state = STATE.ROTATE;
                break;
            case 2: // 双指触摸 - 缩放和平移
                if (enableZoom === false && enablePan === false) return;
                handleTouchStartDollyPan(e);
                state = STATE.DOLLY_PAN;
                break;
            default:
                state = STATE.NONE;
        }
    };

    /**
     * 触摸移动事件处理
     * @param {TouchEvent} e - 触摸事件
     */
    const onTouchMove = (e) => {
        if (!this.enabled) return;
        e.preventDefault();
        e.stopPropagation();

        // 根据触摸点数量执行相应的操作
        switch (e.touches.length) {
            case 1: // 单指移动 - 旋转
                if (enableRotate === false) return;
                handleMoveRotate(e.touches[0].pageX, e.touches[0].pageY);
                break;
            case 2: // 双指移动 - 缩放和平移
                if (enableZoom === false && enablePan === false) return;
                handleTouchMoveDollyPan(e);
                break;
            default:
                state = STATE.NONE;
        }
    };

    /**
     * 触摸结束事件处理
     */
    const onTouchEnd = () => {
        if (!this.enabled) return;
        state = STATE.NONE;
    };

    /**
     * 上下文菜单事件处理（阻止默认行为）
     * @param {Event} e - 事件对象
     */
    const onContextMenu = (e) => {
        if (!this.enabled) return;
        e.preventDefault();
    };

    /**
     * 添加所有事件处理器
     */
    function addHandlers() {
        element.addEventListener('contextmenu', onContextMenu, false);
        element.addEventListener('mousedown', onMouseDown, false);
        element.addEventListener('wheel', onMouseWheel, { passive: false });
        element.addEventListener('touchstart', onTouchStart, { passive: false });
        element.addEventListener('touchend', onTouchEnd, false);
        element.addEventListener('touchmove', onTouchMove, { passive: false });
    }

    /**
     * 移除所有事件处理器
     * 在不再需要控制器时调用此方法以清理事件监听
     */
    this.remove = function () {
        element.removeEventListener('contextmenu', onContextMenu);
        element.removeEventListener('mousedown', onMouseDown);
        element.removeEventListener('wheel', onMouseWheel);
        element.removeEventListener('touchstart', onTouchStart);
        element.removeEventListener('touchend', onTouchEnd);
        element.removeEventListener('touchmove', onTouchMove);
        window.removeEventListener('mousemove', onMouseMove);
        window.removeEventListener('mouseup', onMouseUp);
    };

    // 初始化时添加事件处理器
    addHandlers();
}
