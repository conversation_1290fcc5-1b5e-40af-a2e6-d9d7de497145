<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebGL 部分顶点缓冲区更新示例</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        canvas {
            border: 2px solid #333;
            display: block;
            margin: 20px auto;
            background-color: #000;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        
        .info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .status {
            text-align: center;
            font-weight: bold;
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        
        .status.initial { background-color: #fff3cd; }
        .status.updated1 { background-color: #d4edda; }
        .status.updated2 { background-color: #cce5ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebGL 部分顶点缓冲区更新示例</h1>
        
        <div class="info">
            <h3>功能说明：</h3>
            <p>这个示例展示了如何使用 <code>bufferSubData</code> 来部分更新顶点缓冲区中的数据，而不需要重新创建整个缓冲区。</p>
            <ul>
                <li><strong>初始状态</strong>：显示两个三角形（上方和下方）</li>
                <li><strong>更新上方三角形</strong>：只更新第一个三角形的顶点位置</li>
                <li><strong>更新下方三角形</strong>：只更新第二个三角形的顶点位置</li>
                <li><strong>重置</strong>：恢复到初始状态</li>
            </ul>
        </div>
        
        <canvas id="webglCanvas" width="600" height="400"></canvas>
        
        <div class="controls">
            <button id="updateFirst">更新上方三角形</button>
            <button id="updateSecond">更新下方三角形</button>
            <button id="reset">重置</button>
        </div>
        
        <div id="status" class="status initial">状态：初始显示两个三角形</div>
    </div>

    <script>
        // 顶点着色器源码
        const vertexShaderSource = `
            attribute vec3 a_position;
            void main() {
                gl_Position = vec4(a_position, 1.0);
            }
        `;

        // 片段着色器源码
        const fragmentShaderSource = `
            precision mediump float;
            void main() {
                gl_FragColor = vec4(1.0, 0.5, 0.2, 1.0); // 橙色
            }
        `;

        // VertexBuffer 类
        class VertexBuffer {
            constructor(gl, data, usage = gl.STATIC_DRAW) {
                this.gl = gl;
                this.buffer = gl.createBuffer();
                this.bind();
                gl.bufferData(gl.ARRAY_BUFFER, data, usage);
                this.size = data.length;
            }

            bind() {
                this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffer);
            }

            update(data, offset = 0) {
                this.bind();
                this.gl.bufferSubData(this.gl.ARRAY_BUFFER, offset, data);
            }

            delete() {
                if (this.buffer) {
                    this.gl.deleteBuffer(this.buffer);
                    this.buffer = null;
                }
            }
        }

        // 创建着色器
        function createShader(gl, type, source) {
            const shader = gl.createShader(type);
            gl.shaderSource(shader, source);
            gl.compileShader(shader);
            
            if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
                console.error('着色器编译错误:', gl.getShaderInfoLog(shader));
                gl.deleteShader(shader);
                return null;
            }
            
            return shader;
        }

        // 创建着色器程序
        function createShaderProgram(gl, vertexSource, fragmentSource) {
            const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexSource);
            const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentSource);
            
            const program = gl.createProgram();
            gl.attachShader(program, vertexShader);
            gl.attachShader(program, fragmentShader);
            gl.linkProgram(program);
            
            if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
                console.error('程序链接错误:', gl.getProgramInfoLog(program));
                return null;
            }
            
            return program;
        }

        // 主要的示例类
        class PartialVertexBufferUpdateExample {
            constructor() {
                this.canvas = document.getElementById('webglCanvas');
                this.gl = this.canvas.getContext('webgl');
                this.statusElement = document.getElementById('status');
                
                if (!this.gl) {
                    alert('WebGL 不支持！');
                    return;
                }
                
                this.init();
                this.setupEventListeners();
            }

            init() {
                const gl = this.gl;
                
                // 创建着色器程序
                this.program = createShaderProgram(gl, vertexShaderSource, fragmentShaderSource);
                
                // 初始顶点数据（两个三角形）
                this.initialPositions = new Float32Array([
                    // 三角形1 (上方)
                    0.0, 0.5, 0.0,    // 顶点1
                    -0.5, -0.1, 0.0,  // 顶点2  
                    0.5, -0.1, 0.0,   // 顶点3
                    // 三角形2 (下方)
                    0.0, -0.3, 0.0,   // 顶点4
                    -0.5, -0.9, 0.0,  // 顶点5
                    0.5, -0.9, 0.0    // 顶点6
                ]);
                
                // 创建顶点缓冲区（使用 DYNAMIC_DRAW 以便更新）
                this.positionBuffer = new VertexBuffer(gl, this.initialPositions, gl.DYNAMIC_DRAW);
                this.positionLocation = gl.getAttribLocation(this.program, 'a_position');
                
                // 设置视口和清除颜色
                gl.viewport(0, 0, this.canvas.width, this.canvas.height);
                gl.clearColor(0.1, 0.1, 0.1, 1.0);
                
                // 初始渲染
                this.render();
            }

            updateFirstTriangle() {
                // 更新第一个三角形的数据（使其变得更高更窄）
                const newFirstTriangle = new Float32Array([
                    0.0, 0.8, 0.0,    // 修改第一个顶点 - 更高
                    -0.3, -0.2, 0.0,  // 修改第二个顶点 - 更窄
                    0.3, -0.2, 0.0    // 修改第三个顶点 - 更窄
                ]);
                
                // 部分更新：从偏移量0开始更新
                this.positionBuffer.update(newFirstTriangle, 0);
                this.render();
                this.updateStatus('updated1', '状态：上方三角形已更新（变高变窄）');
            }

            updateSecondTriangle() {
                // 更新第二个三角形的数据（使其变得更宽更矮）
                const newSecondTriangle = new Float32Array([
                    0.0, -0.4, 0.0,   // 修改第四个顶点
                    -0.7, -0.7, 0.0,  // 修改第五个顶点 - 更宽
                    0.7, -0.7, 0.0    // 修改第六个顶点 - 更宽
                ]);
                
                // 部分更新：从偏移量9*4=36字节开始更新（3个顶点*3个分量*4字节）
                this.positionBuffer.update(newSecondTriangle, 9 * 4);
                this.render();
                this.updateStatus('updated2', '状态：下方三角形已更新（变宽变矮）');
            }

            reset() {
                // 重置为初始状态
                this.positionBuffer.update(this.initialPositions, 0);
                this.render();
                this.updateStatus('initial', '状态：已重置为初始状态');
            }

            render() {
                const gl = this.gl;
                
                // 清除画布
                gl.clear(gl.COLOR_BUFFER_BIT);
                
                // 使用着色器程序
                gl.useProgram(this.program);
                
                // 绑定顶点缓冲区并设置属性
                this.positionBuffer.bind();
                gl.enableVertexAttribArray(this.positionLocation);
                gl.vertexAttribPointer(this.positionLocation, 3, gl.FLOAT, false, 0, 0);
                
                // 绘制两个三角形（6个顶点）
                gl.drawArrays(gl.TRIANGLES, 0, 6);
            }

            updateStatus(className, text) {
                this.statusElement.className = `status ${className}`;
                this.statusElement.textContent = text;
            }

            setupEventListeners() {
                document.getElementById('updateFirst').addEventListener('click', () => {
                    this.updateFirstTriangle();
                });
                
                document.getElementById('updateSecond').addEventListener('click', () => {
                    this.updateSecondTriangle();
                });
                
                document.getElementById('reset').addEventListener('click', () => {
                    this.reset();
                });
            }
        }

        // 启动示例
        window.addEventListener('load', () => {
            new PartialVertexBufferUpdateExample();
        });
    </script>
</body>
</html>
